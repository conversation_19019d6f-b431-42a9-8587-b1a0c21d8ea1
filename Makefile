SHELL := /bin/bash
ROOT_DIR:=$(shell dirname $(realpath $(firstword $(MAKEFILE_LIST))))
VENV_PRE_COMMIT := ${ROOT_DIR}/venv/.pre_commit
VENV_DOCS_REBUILD := ${ROOT_DIR}/venv/.docs_rebuild

export RUNNING_IN_MAKE := 1

# Add this near the top with other variable definitions
BRAINSTORE_VERSION ?= latest

.PHONY: all clean
all: ${VENV_PRE_COMMIT} services docs-sdk

.PHONY: py
py: ${VENV_PYTHON_PACKAGES}
	bash -c 'source venv/bin/activate'

.PHONY: services-docker-compose services-bt services

services: services-docker-compose services-bt

services-docker-compose:
	./scripts/docker_compose_pull_retry.py -f services/docker-compose.yml
	docker compose --env-file $$(./scripts/select_docker_env.py) $(DOCKER_COMPOSE_ARGS) -f services/docker-compose.yml up -d --remove-orphans --build --wait --wait-timeout 15 $(DOCKER_COMPOSE_UP_ARGS)

services-grafana:
	docker compose $(DOCKER_COMPOSE_ARGS) -f services/grafana/docker-compose.yaml up -d --remove-orphans --build

services-bt: services-docker-compose
	./services/bt_services.py start $(BT_SERVICES_FILTER) $(BT_SERVICES_START_ARGS)
	./services/bt_services.py healthcheck $(BT_SERVICES_FILTER) $(BT_SERVICES_HEALTHCHECK_ARGS)

.PHONY: services-down services-grafana-down
services-down: services-grafana-down
	./services/bt_services.py stop
	docker compose -f services/docker-compose.yml down

services-grafana-down:
	docker rm -f memcached || true
	docker compose -f services/grafana/docker-compose.yaml down

.PHONY: test
test: py-tests js-tests

.PHONY: py-tests js-tests js-tests-app js-tests-libs js-tests-local

py-tests:
	python -m bt_unittest $(TEST_ARGS)

js-tests: js-tests-app js-tests-libs js-tests-tests js-tests-local

js-tests-app:
	pnpm build --filter "braintrustdata^..." && pnpm test --filter "braintrustdata"

js-tests-local:
	pnpm build --filter "@braintrust/local" && pnpm test --filter "@braintrust/local"

js-tests-libs:
	pnpm build --filter "@braintrust/*^..." && pnpm test --filter "@braintrust/*" --filter "!@braintrust/proxy"

js-tests-tests:
	pnpm test

VENV_INITIALIZED := venv/.initialized

${VENV_INITIALIZED}:
	rm -rf venv && uv venv --python 3.11 venv
	@touch ${VENV_INITIALIZED}

VENV_PYTHON_PACKAGES := venv/.python_packages

${VENV_PYTHON_PACKAGES}: ${VENV_INITIALIZED} submodules-init
	bash -c 'source venv/bin/activate && uv pip install -r requirements.txt -e sdk/core/py -e sdk/py[all] -e autoevals[all] -e local/py -e sdk/integrations/langchain-py[all]'
	@touch $@

${VENV_PRE_COMMIT}: ${VENV_PYTHON_PACKAGES}
	bash -c 'source venv/bin/activate && ./scripts/install_all_precommit_hooks.py'
	@touch $@

DEV_PACKAGE_JSON := dev-package-json

.PHONY: ${DEV_PACKAGE_JSON}
${DEV_PACKAGE_JSON}:
	pnpm install

develop: ${VENV_PRE_COMMIT} ${DEV_PACKAGE_JSON} install-rust install-aws-cli

.PHONY: install-aws-cli
install-aws-cli:
	./scripts/install_aws_cli.sh

.PHONY: setup-aws
setup-aws: install-aws-cli
	./scripts/setup_aws_config.py

.PHONY: install-terraform
install-terraform:
	brew tap hashicorp/tap
	brew install hashicorp/tap/terraform

.PHONY: install-rust install-rust-toolchain install-watchexec install-wasm-pack install-cargo-fmt

install-rust: install-rust-toolchain install-watchexec install-wasm-pack install-cargo-fmt

install-rust-toolchain:
	@if ! command -v cargo > /dev/null; then \
		echo "cargo not found. Please install Rust by running:"; \
		echo "curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh"; \
		exit 1; \
	fi
	@if ! cargo --list | grep binstall > /dev/null; then \
		echo "cargo binstall not found. Please install by running:"; \
		echo "curl -L --proto '=https' --tlsv1.2 -sSf https://raw.githubusercontent.com/cargo-bins/cargo-binstall/main/install-from-binstall-release.sh | bash"; \
		exit 1; \
	fi

install-watchexec: install-rust-toolchain
	@if ! command -v watchexec > /dev/null; then \
		cargo binstall watchexec-cli -y; \
	fi

install-wasm-pack: install-rust-toolchain
	@if ! command -v wasm-pack > /dev/null; then \
		cargo binstall wasm-pack -y; \
	fi

install-cargo-fmt: install-rust-toolchain
	@if ! rustup component list --installed | grep -q 'rustfmt'; then \
		rustup component add rustfmt; \
	fi

fixup:
	pre-commit run --all-files

.PHONY: publish-lambdas
publish-lambdas: api-ts api-schema
	./scripts/publish_lambdas.sh $(ARGS)

.PHONY: submodules-init
submodules-init:
	git submodule sync
	git submodule update --init --recursive

REGIONS := us-east-1 us-east-2 us-west-2
REGION_FILES := $(addsuffix .yaml,$(addprefix api/out/sam-packaged-,$(REGIONS)))
REGION_VPC_FILES := $(addsuffix .yaml,$(addprefix api/out-vpc/sam-packaged-,$(REGIONS)))
REGION_UPLOAD := $(addsuffix .upload,$(addprefix api/out/sam-packaged-,$(REGIONS)))

API_PY_FILES = $(shell find api -name "*.py")

api/out/sam.yaml: ${API_PY_FILES} api/.chalice/config.json api/merge.yaml scripts/inject_js_api.py
	cd api \
		&& ../scripts/wchalice.py package out --merge-template merge.yaml \
		&& ../scripts/inject_js_api.py out/sam.yaml

api/out-vpc/sam.yaml: ${API_PY_FILES} api/.chalice/config.json api/merge-vpc.yaml scripts/inject_js_api.py
	cd api \
		&& ../scripts/wchalice.py package out-vpc --merge-template merge-vpc.yaml \
		&& ../scripts/inject_js_api.py out-vpc/sam.yaml \
		&& ../scripts/cleanup_staging_resources.py out-vpc/sam.yaml

api/out/sam-packaged-%.yaml: api/out/sam.yaml api-ts api-schema
	cd api/out \
		&& aws cloudformation package --template-file ./sam.yaml --s3-bucket braintrust-cf-$* --output-template-file sam-packaged-$*.yaml \
		&& ${ROOT_DIR}/scripts/fixup_sam.py sam-packaged-$*.yaml

api/out-vpc/sam-packaged-%.yaml: api/out-vpc/sam.yaml api-ts api-schema api/merge-vpc.yaml
	cd api/out-vpc \
		&& aws cloudformation package --template-file ./sam.yaml --s3-bucket braintrust-cf-$* --output-template-file sam-packaged-$*.yaml \
		&& ${ROOT_DIR}/scripts/fixup_sam.py sam-packaged-$*.yaml

.PHONY: chalice-packaged chalice-cf update-cf-prod update-cf-staging api/merge.yaml

api/merge.yaml: scripts/merge_base.py api/merge-base.yaml
	./scripts/merge_base.py api/merge-base.yaml api/merge.yaml --brainstore-version $(BRAINSTORE_VERSION)

api/merge-vpc.yaml: scripts/merge_vpc.py api/merge.yaml
	./scripts/merge_vpc.py api/merge.yaml api/merge-vpc.yaml

chalice-packaged: $(REGION_FILES) $(REGION_VPC_FILES)

# Just use us-east-1 to compute the MD5, so that it's consistent across regions
api/out/sam-packaged-%.upload: api/out/sam-packaged-%.yaml api/out-vpc/sam-packaged-%.yaml api/out/sam-packaged-us-east-1.yaml
	$(eval MD5 := $(shell md5sum api/out/sam-packaged-us-east-1.yaml | awk '{print $$1}'))
	cd api/out \
		&& aws s3 cp sam-packaged-$*.yaml s3://braintrust-cf-$*/braintrust-$(MD5).yaml
	cd api/out-vpc \
		&& aws s3 cp sam-packaged-$*.yaml s3://braintrust-cf-$*/braintrust-$(MD5)-vpc.yaml
	touch $@
	echo "Templates for $* are available at: " | tee api/out/templates-$*.txt
	echo "https://braintrust-cf-$*.s3.amazonaws.com/braintrust-$(MD5).yaml" | tee -a api/out/templates-$*.txt
	echo "https://braintrust-cf-$*.s3.amazonaws.com/braintrust-$(MD5)-vpc.yaml" | tee -a api/out/templates-$*.txt

# Package and push the latest templates
chalice-cf: $(REGION_UPLOAD)
	cat api/out/templates-*.txt

update-cf-staging: api/out-vpc/sam-packaged-us-east-1.yaml api/out/sam-packaged-us-east-1.upload
	$(eval MD5 := $(shell md5sum api/out/sam-packaged-us-east-1.yaml | awk '{print $$1}'))
	braintrust install api bt-staging --update-template --template https://braintrust-cf-us-east-1.s3.amazonaws.com/braintrust-$(MD5)-vpc.yaml

update-cf-prod: api/out/sam-packaged-us-east-1.yaml api/out/sam-packaged-us-east-1.upload
	$(eval MD5 := $(shell md5sum api/out/sam-packaged-us-east-1.yaml | awk '{print $$1}'))
	braintrust install api bt3subnets-3 --update-template --template https://braintrust-cf-us-east-1.s3.amazonaws.com/braintrust-$(MD5).yaml

# Make templates available to public users
.PHONY: publish-cf
publish-cf:
	@if [ "$(CI)" != "true" ]; then \
		echo "WARNING: This will make your local CF templates available to public users."; \
		read -p "Are you sure you want to continue? [y/N] " answer; \
		if [ "$$answer" != "y" ]; then \
			echo "Aborting."; \
			exit 1; \
		fi \
	fi

	$(eval TAG := $(if $(ARGS),$(ARGS),latest))

	aws s3 cp api/out/sam-packaged-us-east-1.yaml s3://braintrust-cf/braintrust-$(TAG).yaml
	for region in $(REGIONS); do \
		aws s3 cp api/out/sam-packaged-$$region.yaml s3://braintrust-cf-$$region/braintrust-$(TAG).yaml; \
	done

	aws s3 cp api/out-vpc/sam-packaged-us-east-1.yaml s3://braintrust-cf/braintrust-staging-$(TAG).yaml
	for region in $(REGIONS); do \
		aws s3 cp api/out-vpc/sam-packaged-$$region.yaml s3://braintrust-cf-$$region/braintrust-staging-$(TAG).yaml; \
	done

	$(eval TEMPLATE_FILE := api/out/template_locations.$(TAG).txt)
	echo "Templates for tag '$(TAG)'" | tee $(TEMPLATE_FILE)
	for region in $(REGIONS); do \
		echo "$$region:" | tee -a $(TEMPLATE_FILE); \
		echo "https://braintrust-cf-$$region.s3.amazonaws.com/braintrust-$(TAG).yaml" | tee -a $(TEMPLATE_FILE); \
		echo "https://braintrust-cf-$$region.s3.amazonaws.com/braintrust-staging-$(TAG).yaml" | tee -a $(TEMPLATE_FILE); \
		echo "" | tee -a $(TEMPLATE_FILE); \
	done

.PHONY: dev-quarantine

dev-quarantine: api/dev-quarantine.yaml
	aws cloudformation update-stack --stack-name Dev-Quarantine \
		--template-body file://$(shell pwd)/api/dev-quarantine.yaml \
		--parameters ParameterKey=QuarantineVPCCIDR,ParameterValue=**********/16 \
		--capabilities CAPABILITY_NAMED_IAM
	@echo "Waiting for CloudFormation stack update to complete..."
	@aws cloudformation wait stack-update-complete --stack-name Dev-Quarantine
	@echo "CloudFormation stack update completed."

api/dev-quarantine.yaml: api/dev-quarantine-base.yaml ./scripts/merge_base.py
	./scripts/merge_base.py api/dev-quarantine-base.yaml api/dev-quarantine.yaml --quarantine-only

SDK_VERSION=$(shell python -c 'from sdk.py.src.braintrust.version import VERSION; print(VERSION)')
AUTOEVALS_VERSION=$(shell python -c 'from autoevals.version import VERSION; print(VERSION)')
PY_CORE_VERSION=$(shell python -c 'from sdk.core.py.src.braintrust_core.version import VERSION; print(VERSION)')

.PHONY: build-sdk publish-sdk-impl build-sdk-js publish-sdk-js-impl clean-sdk docs-sdk publish-sdk

build-sdk: build-sdk-js

publish-sdk-impl: publish-sdk-js-impl

build-sdk-js:
	cd sdk/js && npm run build

publish-sdk-js-impl: build-sdk-js
	cd sdk/js && ./scripts/validate-release.sh
	cd sdk/js && npm publish

clean-sdk:
	rm -rf sdk/py/dist/*
	rm -rf sdk/js/dist

publish-sdk: publish-sdk-impl
	./scripts/check_package_consistency_message.py

.PHONY: build-autoevals publish-autoevals-impl build-autoevals-py build-autoevals-js publish-autoevals-py-impl publish-autoevals-js-impl clean-autoevals publish-autoevals publish-langchain-py

build-autoevals: build-autoevals-py build-autoevals-js

publish-autoevals-impl: publish-autoevals-py-impl publish-autoevals-js-impl

build-autoevals-py:
	./scripts/py_prepublish_autoevals.py
	cd autoevals && python3 -m build
	./scripts/py_postpublish_autoevals.py

publish-autoevals-py-impl: build-autoevals-py
	cd autoevals && python3 -m twine upload dist/autoevals-${AUTOEVALS_VERSION}*

build-autoevals-js:
	cd autoevals && npm run build

publish-autoevals-js-impl: build-autoevals-js
	cd autoevals && npm publish

clean-autoevals:
	rm -rf autoevals/dist/*
	rm -rf autoevals/jsdist

publish-autoevals: publish-autoevals-impl
	./scripts/check_package_consistency_message.py

PY_LANGCHAIN_VERSION=$(shell PYTHONPATH=sdk/integrations/langchain-py/src python -c 'from braintrust_langchain.version import VERSION; print(VERSION)')

publish-langchain-py:
	cd sdk/integrations/langchain-py && \
	python -m build && \
	python -m twine upload dist/braintrust_langchain-${PY_LANGCHAIN_VERSION}*

publish-langchain-js:
	cd sdk/integrations/langchain-js && \
	pnpm build && \
	pnpm publish

.PHONY: build-core publish-core-impl build-core-py build-core-js publish-core-py-impl publish-core-js-impl clean-core publish-core publish-langchain-js

build-core-py:
	cd sdk/core/py && python3 -m build

publish-core-py-impl: build-core-py
	cd sdk/core/py && python3 -m twine upload dist/braintrust_core-${PY_CORE_VERSION}*

build-core-js:
	cd sdk/core/js && npm run build

publish-core-js-impl: build-core-js
	cd sdk/core/js && npm publish

clean-core:
	rm -rf sdk/core/py/dist/*
	rm -rf sdk/core/js/dist

clean: clean-sdk clean-autoevals clean-core
	# Clean JavaScript/TypeScript build artifacts
	# rm -rf node_modules/  # this takes a long time
	rm -rf dist/
	rm -rf ./app/dist/
	rm -rf ./api-ts/dist/
	rm -rf ./api-ts/node_modules/
	rm -rf ./btql/dist/
	rm -rf ./otel/dist/
	rm -rf ./realtime/dist/
	rm -rf ./test-proxy/dist/
	# Clean TypeScript build cache
	find . -name "*.tsbuildinfo" -delete 2>/dev/null || true
	# Clean build cache directories
	find . -name ".turbo" -type d -exec rm -rf {} + 2>/dev/null || true
	find . -name ".next" -type d -exec rm -rf {} + 2>/dev/null || true
	find . -name ".source" -type d -exec rm -rf {} + 2>/dev/null || true
	find . -name ".vercel" -type d -exec rm -rf {} + 2>/dev/null || true
	# Clean Rust/WASM artifacts
	rm -rf ./brainstore/btql-wasm/dist/
	rm -rf ./brainstore/btql-wasm/node_modules/
	cd brainstore && cargo clean
	# Clean Python build artifacts
	find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
	find . -type d -name "*.egg-info" -exec rm -rf {} + 2>/dev/null || true
	find . -type f -name "*.pyc" -delete 2>/dev/null || true

publish-core-js: publish-core-js-impl
	./scripts/check_package_consistency_message.py

publish-core-py: publish-core-py-impl
	./scripts/check_package_consistency_message.py

.PHONY: docs-sdk docs-sdk-js docs-sdk-py

SDK_PY_FILES := $(shell find $(ROOT_DIR)/sdk/py/src -name "*.py")
SDK_JS_FILES := $(shell find $(ROOT_DIR)/sdk/js -name "*.ts" -o -name "*.js")
AUTOEVALS_JS_FILES := $(shell find $(ROOT_DIR)/autoevals -name "*.ts" -o -name "*.js")

DOCS_API_FILE := ./app/content/docs/reference/api/sections.json
DOCS_TEMPLATES := $(shell find $(ROOT_DIR)/api-docs/templates -name "*.dot" -o -name "*.def")
PUBLIC_OPENAPI_SPEC_JSON := $(ROOT_DIR)/openapi/openapi/spec.json
PUBLIC_OPENAPI_SPEC_YAML := $(ROOT_DIR)/openapi/openapi/spec.yaml

docs-sdk: ${VENV_DOCS_REBUILD}

${VENV_DOCS_REBUILD}: docs-sdk-js docs-sdk-py docs-api docs-cookbook
	$(eval SDK_MD_FILES := $(shell find $(ROOT_DIR)/app/content/docs/ -name "*.md" -o -name "*.json"))
	pre-commit run end-of-file-fixer --color never --files $(SDK_MD_FILES) || true > /dev/null
	pre-commit run prettier --color never --files $(SDK_MD_FILES) || true > /dev/null
	touch ${VENV_DOCS_REBUILD}

# https://niklasrosenstein.github.io/pydoc-markdown/just-generate-me-some-markdown/
docs-sdk-py: app/content/docs/reference/libs/python.md app/content/docs/reference/autoevals/index.mdx app/content/docs/reference/autoevals/python.mdx

app/content/docs/reference/libs/python.md: $(SDK_PY_FILES)
	echo -e "---\ntitle: Python\ndescription: Python reference for Braintrust's core SDK.\n---\n" > app/content/docs/reference/libs/python.md
	pydoc-markdown -I sdk/py/src -m braintrust -m braintrust.logger -m braintrust.framework -m braintrust.functions.stream -m braintrust.functions.invoke  >> app/content/docs/reference/libs/python.md
docs-sdk-js: app/content/docs/reference/libs/nodejs/index.md app/content/docs/reference/autoevals/nodejs/index.md

.PHONY: docs-api
docs-api: ${PUBLIC_OPENAPI_SPEC_JSON} ${PUBLIC_OPENAPI_SPEC_YAML} ${DOCS_TEMPLATES} ./scripts/downgrade_titles.py

# This command runs typedoc and then fixes up js/modules.md to js.md
app/content/docs/reference/libs/nodejs/index.md: ${SDK_JS_FILES} $(ROOT_DIR)/sdk/js/typedoc.json $(ROOT_DIR)/sdk/js/package.json
	cd sdk/js && npm run docs
	rm -f app/content/docs/reference/libs/nodejs/README.md
	echo -e "---\ntitle: TypeScript\ndescription: TypeScript reference for Braintrust's core SDK.\n---\n" > app/content/docs/reference/libs/nodejs/index.md
	cat app/content/docs/reference/libs/nodejs/modules.md >> app/content/docs/reference/libs/nodejs/index.md
	rm -f app/content/docs/reference/libs/nodejs/modules.md
	find app/content/docs/reference/libs -type f -exec ./scripts/remove_module.py {} +
	./scripts/add_default_frontmatter.py app/content/docs/reference/libs/nodejs/classes
	./scripts/add_default_frontmatter.py app/content/docs/reference/libs/nodejs/interfaces
	./scripts/add_default_frontmatter.py app/content/docs/reference/libs/nodejs/modules
	./scripts/fix_meta.py app/content/docs/reference/libs/nodejs/interfaces

AE_PY_FILES := $(shell find $(ROOT_DIR)/autoevals/py -name "*.py")
AE_JS_FILES := $(shell find $(ROOT_DIR)/autoevals/js -name "*.ts" -o -name "*.js")

app/content/docs/reference/autoevals/index.mdx: ${AE_PY_FILES} autoevals/README.md
	./scripts/update_autoevals_index_docs.py app/content/docs/reference/autoevals/index.mdx
	./scripts/convert_tabs.py app/content/docs/reference/autoevals/index.mdx

app/content/docs/reference/autoevals/python.mdx: ${AE_PY_FILES}
	echo -e "---\ntitle: Python\ndescription: Python reference for Braintrust's autoevals library.\n---\n" > app/content/docs/reference/autoevals/python.mdx
	pydoc-markdown pydoc-markdown.yml -I autoevals/py -m autoevals -m autoevals.llm -m autoevals.ragas -m autoevals.moderation -m autoevals.string -m autoevals.number -m autoevals.json -m autoevals.value >> app/content/docs/reference/autoevals/python.mdx
	./scripts/downgrade_titles.py --ignore-first app/content/docs/reference/autoevals/python.mdx
	./scripts/docs_fix_codeblock_indentation.py app/content/docs/reference/autoevals/python.mdx

app/content/docs/reference/autoevals/nodejs/index.md: ${AUTOEVALS_JS_FILES} $(ROOT_DIR)/sdk/js/typedoc.json $(ROOT_DIR)/autoevals/package.json scripts/autoeval_typedoc.py
	./scripts/autoeval_typedoc.py
	cd autoevals && npx typedoc --options typedoc.json js/index.ts
	rm -f app/content/docs/reference/autoevals/nodejs/README.md
	echo -e "---\ntitle: TypeScript\ndescription: TypeScript reference for Braintrust's autoevals library.\n---\n" > app/content/docs/reference/autoevals/nodejs/index.md
	cat app/content/docs/reference/autoevals/nodejs/modules.md >> app/content/docs/reference/autoevals/nodejs/index.md
	rm -f app/content/docs/reference/autoevals/nodejs/modules.md
	find app/content/docs/reference/autoevals -type f -exec ./scripts/remove_module.py {} +
	./scripts/add_default_frontmatter.py app/content/docs/reference/autoevals/nodejs/classes
	./scripts/add_default_frontmatter.py app/content/docs/reference/autoevals/nodejs/interfaces
	./scripts/add_default_frontmatter.py app/content/docs/reference/autoevals/nodejs/modules
	./scripts/fix_meta.py app/content/docs/reference/autoevals/nodejs/interfaces

.PHONY: docs-cookbook cookbook-watch cookbook-bundler cookbook-schemas
docs-cookbook: cookbook-bundler
	cd cookbook/bundler && node ./dist/index.js

cookbook-watch: cookbook-schemas
	cd cookbook/bundler && npx nodemon ./dist/index.js -y

cookbook-bundler: cookbook-schemas
	cd cookbook/bundler && pnpm build

cookbook-schemas: cookbook/bundler/src/registry-schema.ts cookbook/bundler/src/authors-schema.ts

cookbook/bundler/src/registry-schema.ts: ./cookbook/content/.github/registry_schema.json
	cd cookbook/bundler && npx json-schema-to-zod -i ../content/.github/registry_schema.json -o src/registry-schema.ts

cookbook/bundler/src/authors-schema.ts: ./cookbook/content/.github/authors_schema.json
	cd cookbook/bundler && npx json-schema-to-zod -i ../content/.github/authors_schema.json -o src/authors-schema.ts

.PHONY: api-ts api-ts-dev
api-ts: submodules-init
	pnpm build --filter "@braintrust/api-ts"

proxy/apis/node/.env.local:
	echo 'BRAINTRUST_APP_URL="http://localhost:3000"' > proxy/apis/node/.env.local
	echo 'REDIS_HOST="127.0.0.1"' >> proxy/apis/node/.env.local
	echo 'REDIS_PORT=6479' >> proxy/apis/node/.env.local

api-ts-dev:
	cd api-ts && pnpm dev

.PHONY: ai-proxy-cloudflare-deploy ai-proxy-cloudflare-dev
ai-proxy-cloudflare-deploy:
	cd proxy/apis/cloudflare && npx wrangler deploy

ai-proxy-cloudflare-dev:
	cd proxy/apis/cloudflare && npx wrangler dev

.PHONY: api-schema
api-schema: api-schema/deployment.zip

api-schema/deployment.zip: api-schema/lambda_function.py api-schema/lambda_function_requirements.txt api-schema/build_lambda.sh $(shell find api-schema/migrations -type f)
	cd api-schema && ./build_lambda.sh

.PHONY: openapi-spec

openapi-spec: ${PUBLIC_OPENAPI_SPEC_JSON} ${PUBLIC_OPENAPI_SPEC_YAML}

.PHONY: openapi-spec-diff

openapi-spec-diff:
	pnpm build --filter '@braintrust/openapi-deployment'
	@diff ${PUBLIC_OPENAPI_SPEC_JSON} <(npx generate-openapi-spec api)
	@diff ${PUBLIC_OPENAPI_SPEC_YAML} <(npx tsx scripts/convert_json_to_yaml.ts ${PUBLIC_OPENAPI_SPEC_JSON})

${PUBLIC_OPENAPI_SPEC_JSON}: $(shell find deployment-internal/openapi/src -name "*.ts") $(shell find sdk/core/js/src/ -name "*.ts") $(shell find sdk/core/js/typespecs/ -name "*.ts") build-core-js build-sdk-js
	pnpm build --filter '@braintrust/openapi-deployment'
	npx generate-openapi-spec api > ${PUBLIC_OPENAPI_SPEC_JSON}

${PUBLIC_OPENAPI_SPEC_YAML}: ${PUBLIC_OPENAPI_SPEC_JSON}
	npx tsx scripts/convert_json_to_yaml.ts ${PUBLIC_OPENAPI_SPEC_JSON} > ${PUBLIC_OPENAPI_SPEC_YAML}

.PHONY: python-sdk-types

PYTHON_SDK_TYPES_FILE := $(ROOT_DIR)/sdk/py/src/braintrust/_types.py

python-sdk-types: ${PYTHON_SDK_TYPES_FILE}

${PYTHON_SDK_TYPES_FILE}: $(shell find deployment-internal/openapi/src -name "*.ts") $(shell find sdk/core/js/src/ -name "*.ts") $(shell find sdk/core/js/typespecs/ -name "*.ts") build-core-js build-sdk-js
	python scripts/generate_python_sdk_types.py --write

.PHONY: python-sdk-types-diff

python-sdk-types-diff:
	@diff ${PYTHON_SDK_TYPES_FILE} <(python scripts/generate_python_sdk_types.py)

.PHONY: deployment-docker-compose

generate-docker-compose:
	# Disabling this for now. We can run it ad-hoc for specific customers as needed.
	# If you re-enable it, then update the .github/actions/test/action.yaml to check it.
	# generate-docker-compose full > deployment/docker/docker-compose.full.yml
	generate-docker-compose api > deployment/docker/docker-compose.api.yml


.PHONY: brainstore brainstore-tests brainstore-tests-update brainstore-docker-debug

# In the absence of clearer dependencies, just run this every time.
.PHONY: brainstore/examples/braintrust/schema.json
brainstore/examples/braintrust/schema.json:
	cd local/js && npx tsx scripts/dump-logical-schema.ts > ../../brainstore/examples/braintrust/schema.json

brainstore:
	cd brainstore && cargo build

brainstore-tests:
	cd brainstore && cargo test -- --nocapture

brainstore-tests-update:
	cd brainstore && UPDATE=1 cargo test -- --nocapture

brainstore-init-buckets-tests:
	if ! aws s3 ls | grep code-bundles; then aws s3 mb s3://code-bundles; fi

# Build brainstore Docker image with optional debug symbol upload to Polar Signals
# Usage:
#   make brainstore-docker-debug  # Build without debug symbol upload
#   BRAINSTORE_PROFILING_API_KEY=xxx make brainstore-docker-debug  # Build with debug symbol upload
#   BRAINSTORE_DOCKER_TAG=custom-tag make brainstore-docker-debug  # Custom tag
#
# Note: Uses Docker BuildKit secrets when API key is provided to avoid exposing
# sensitive data in image layers or build history
brainstore-docker-debug:
	cd brainstore && GIT_COMMIT_HASH=$$(git rev-parse HEAD) && \
	if [ -n "$${BRAINSTORE_PROFILING_API_KEY}" ]; then \
		echo "Building with debug symbols..."; \
		DOCKER_BUILDKIT=1 docker build \
			--build-arg GIT_COMMIT_HASH=$${GIT_COMMIT_HASH} \
			--secret id=polar_signals_api_key,env=BRAINSTORE_PROFILING_API_KEY \
			--progress=plain \
			-t brainstore .; \
	else \
		echo "Building without debug symbol upload..."; \
		docker build \
			--build-arg GIT_COMMIT_HASH=$${GIT_COMMIT_HASH} \
			-t brainstore .; \
	fi
	aws ecr-public get-login-password --region us-east-1 | docker login --username AWS --password-stdin public.ecr.aws
	docker tag brainstore:latest public.ecr.aws/braintrust/brainstore:$(or ${BRAINSTORE_DOCKER_TAG},debug-aarch64)
	docker push public.ecr.aws/braintrust/brainstore:$(or ${BRAINSTORE_DOCKER_TAG},debug-aarch64)

migrate-app:
	cd app && npx supabase migration up

migrate-api:
	python api-schema/lambda_function.py --execute --run-all-in-foreground

migrate-dbs: migrate-app migrate-api

brainstore-docker-hotfix:
	BRAINSTORE_DOCKER_TAG=hotfix-aarch64 make brainstore-docker-debug

overmind:
	overmind start -N all -T

.PHONY: btql-ast-diff
sdk/js/btql/ast.ts:
	cp btql/parser/ast.ts sdk/js/src/btql/ast.ts

btql-ast-diff:
	@diff sdk/js/btql/ast.ts btql/parser/ast.ts
