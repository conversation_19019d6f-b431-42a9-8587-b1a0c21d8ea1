use std::{
    collections::HashMap,
    str::FromStr,
    sync::{atomic::AtomicU64, Arc, Mutex},
};

use clap::Parser;
use serde::{Deserialize, Serialize};
use storage::{
    backfill::BackfillWorkerOptions,
    config_with_store::{ConfigWithStore, ConfigWithStoreOpts},
    directory::cached_directory::FileCacheOpts,
    global_store::GlobalStore,
    index_wal_reader::IndexWalReaderOpts,
    limits::{get_or_reset_global_limits, GlobalLimitArgs},
    optimize_tantivy_index::OptimizeObjectTuningOptions,
    retention_worker::TimeBasedRetentionOptions,
    vacuum_index::VacuumIndexOptions,
};
use util::{
    anyhow::{anyhow, Result},
    config::{Config, StorageConfig},
    system_types::FullObjectIdOwned,
    url::Url,
    url_util::str_to_url,
    uuid::Uuid,
};

use crate::{command_proc::CommandProc, executor_pool::ExecutorPool};
#[derive(Parse<PERSON>, Debug, Clone, Serialize, Deserialize, Default)]
pub struct BaseArgs {
    #[cfg_attr(feature = "distribute", arg(short, long, action = clap::ArgAction::Count, value_parser = clap::value_parser!(u8).range(0..=1), env = "BRAINSTORE_VERBOSE"))]
    #[cfg_attr(not(feature = "distribute"), arg(short, long, action = clap::ArgAction::Count, env = "BRAINSTORE_VERBOSE"))]
    pub verbose: u8,

    #[command(flatten)]
    pub telemetry_args: agent::setup_tracing::TelemetryArgs,

    #[arg(
        short,
        long,
        help = "Path to the config file",
        default_value_t = default_config_path(),
        env = "BRAINSTORE_CONFIG"
    )]
    pub config: String,

    #[command(flatten)]
    pub storage_config: StorageConfigArgs,

    #[command(flatten)]
    pub file_cache_opts: FileCacheOpts,

    #[command(flatten)]
    pub limits: GlobalLimitArgs,

    #[arg(
        long,
        help = "Enable pretty logging (useful for console debugging)",
        env = "BRAINSTORE_PRETTY_LOGGING"
    )]
    pub pretty_logging: Option<bool>,

    #[arg(
        long,
        help = "If verbosity is INFO, avoid printing the most verbose INFO-level messages",
        env = "BRAINSTORE_SUPPRESS_VERBOSE_INFO"
    )]
    pub suppress_verbose_info: bool,

    // For simplicity purpose, we'll always let you set these values, but we'll only check the
    // license if the distribute flag is set.
    // This is used for license checks and metrics reporting.
    #[arg(
        long,
        env = "BRAINTRUST_PROD_APP_URL",
        default_value = "https://www.braintrust.dev"
    )]
    pub prod_app_url: String,

    #[arg(long, env = "BRAINSTORE_LICENSE_KEY")]
    pub license_key: Option<String>,

    // This URL is used by time-based retention to resolve project IDs and retention policies
    // from the control plane.
    #[arg(
        long,
        env = "BRAINTRUST_APP_URL",
        default_value = "https://www.braintrust.dev"
    )]
    pub app_url: String,
}

#[derive(Parser, Debug, Clone, Serialize, Deserialize)]
pub struct CLIArgs<T: clap::Args> {
    #[command(flatten)]
    pub base: BaseArgs,

    #[command(flatten)]
    pub args: T,
}

#[derive(Debug, Clone, Default, Parser, Serialize, Deserialize)]
pub struct StorageConfigArgs {
    #[arg(short, long, env = "BRAINSTORE_METADATA_URI")]
    pub metadata_uri: Option<String>,

    #[arg(short, long, env = "BRAINSTORE_WAL_URI")]
    pub wal_uri: Option<String>,

    #[arg(short, long, env = "BRAINSTORE_REALTIME_WAL_URI")]
    pub realtime_wal_uri: Option<String>,

    #[arg(short, long, env = "BRAINSTORE_INDEX_URI")]
    pub index_uri: Option<String>,

    #[arg(short, long, env = "BRAINSTORE_LOCKS_URI")]
    pub locks_uri: Option<String>,

    #[arg(long, env = "BRAINSTORE_LOCKS_MANAGER_ENABLE_BOOKKEEPING")]
    pub locks_manager_enable_bookkeeping: Option<bool>,

    #[arg(short, long, env = "BRAINSTORE_XACT_MANAGER_URI")]
    pub xact_manager_uri: Option<String>,
}

fn default_config_path() -> String {
    if std::path::Path::new("brainstore.json").exists() {
        "brainstore.json".to_string()
    } else {
        "brainstore.yaml".to_string()
    }
}

pub fn load_config_if_exists(path: &str) -> Result<Config> {
    if std::path::Path::new(path).exists() {
        Ok(Config::from_file(path)?)
    } else {
        Ok(Config::default())
    }
}

pub fn coalesce_storage_config(
    config: &Config,
    storage_config: &StorageConfigArgs,
) -> Result<StorageConfig> {
    Ok(StorageConfig {
        metadata_uri: storage_config
            .metadata_uri
            .as_ref()
            .map(|s| str_to_url(s, None))
            .transpose()?
            .or(config.storage.as_ref().map(|s| s.metadata_uri.clone()))
            .ok_or_else(|| anyhow!("No metadata URI provided"))?,
        wal_uri: storage_config
            .wal_uri
            .as_ref()
            .map(|s| str_to_url(s, None))
            .transpose()?
            .or(config.storage.as_ref().map(|s| s.wal_uri.clone()))
            .ok_or_else(|| anyhow!("No WAL URI provided"))?,
        realtime_wal_uri: storage_config
            .realtime_wal_uri
            .as_ref()
            .map(|s| str_to_url(s, None))
            .transpose()?
            .or_else(|| {
                config
                    .storage
                    .as_ref()
                    .and_then(|s| s.realtime_wal_uri.clone())
            }),
        index_uri: storage_config
            .index_uri
            .as_ref()
            .map(|s| str_to_url(s, None))
            .transpose()?
            .or(config.storage.as_ref().map(|s| s.index_uri.clone()))
            .ok_or_else(|| anyhow!("No index URI provided"))?,
        locks_uri: storage_config
            .locks_uri
            .as_ref()
            .map(|s| str_to_url(s, None))
            .transpose()?
            .or(config.storage.as_ref().map(|s| s.locks_uri.clone()))
            .unwrap_or_else(|| Url::from_str("memory://").unwrap()),
        locks_manager_enable_bookkeeping: storage_config
            .locks_manager_enable_bookkeeping
            .unwrap_or(false),
        xact_manager_uri: storage_config
            .xact_manager_uri
            .as_ref()
            .map(|s| str_to_url(s, None))
            .transpose()?
            .or(config.storage.as_ref().map(|s| s.xact_manager_uri.clone()))
            .unwrap_or_else(|| Url::from_str("memory://").unwrap()),
    })
}

#[derive(Default, Clone, Serialize)]
pub struct WebOpts {
    pub process_wal_options: storage::process_wal::ProcessObjectWalOptions,
    pub compact_wal_options: storage::process_wal::CompactSegmentWalOptions,
    pub optimize_all_objects_input: crate::index::OptimizeAllObjectsInput,
    pub optimize_object_tuning_opts: OptimizeObjectTuningOptions,
    pub vacuum_options: VacuumOptions,
    pub time_based_retention_worker_options: TimeBasedRetentionWorkerOptions,
    pub index_wal_reader_opts: IndexWalReaderOpts,
    pub streaming_query_buffer_size: usize,
    pub query_timeout_seconds: usize,
    pub slow_query_log_threshold_seconds: usize,
    pub backfill_worker_options: BackfillWorkerOptions,
}

pub struct AppState {
    pub base_args: BaseArgs,
    pub storage_config: StorageConfig,
    pub config: ConfigWithStore,
    pub config_schema: Option<util::schema::Schema>,
    pub default_logical_schema: Option<String>,
    pub process_list: ProcessList,
    pub command_proc: Option<CommandProc>,
    pub executor_pool: Option<ExecutorPool>,
}

impl AppState {
    pub fn new(args: &BaseArgs) -> Result<Arc<Self>> {
        let config_file = load_config_if_exists(&args.config)?;
        let storage_config = coalesce_storage_config(&config_file, &args.storage_config)?;

        let config = ConfigWithStore::from_config(
            storage_config.clone(),
            ConfigWithStoreOpts {
                file_cache_opts: args.file_cache_opts.clone(),
            },
        )?;

        // Initialize the global limits.
        get_or_reset_global_limits(Some(args.limits.clone()));

        let state = Arc::new(AppState {
            base_args: args.clone(),
            storage_config,
            config,
            config_schema: config_file.schema,
            default_logical_schema: None,
            process_list: ProcessList::new(),
            command_proc: None,
            executor_pool: None,
        });

        Ok(state)
    }

    pub fn set_default_logical_schema(&mut self, schema: Option<String>) {
        self.default_logical_schema = schema;
    }

    pub fn set_command_proc(&mut self, command_proc: Option<CommandProc>) {
        self.command_proc = command_proc;
    }

    pub fn set_executor_pool(&mut self, executor_pool: ExecutorPool) {
        self.executor_pool = Some(executor_pool);
    }
}

#[derive(Debug, Clone, Parser, Serialize, Deserialize)]
pub struct SegmentIdArgs {
    #[arg(help = "The segment IDs to use", env = "BRAINSTORE_SEGMENT_IDS")]
    #[serde(default)]
    pub segment_ids: Vec<Uuid>,

    #[arg(long, help = "Use all segments", env = "BRAINSTORE_SEGMENT_ALL")]
    #[serde(default)]
    pub all: bool,

    #[arg(
        long,
        help = "Optionally specify the object ID for the segments. This will be used to create the schema for the segments or derive the segment IDs if you specify --all",
        env = "BRAINSTORE_SEGMENT_OBJECT_ID"
    )]
    pub object_id: Option<String>,
}

impl SegmentIdArgs {
    fn override_object_id(&self) -> Result<Option<FullObjectIdOwned>> {
        self.object_id
            .as_ref()
            .map(|object_id| object_id.parse())
            .transpose()
    }

    pub async fn segment_ids(&self, global_store: &dyn GlobalStore) -> Result<Vec<Uuid>> {
        if self.segment_ids.is_empty() && !self.all {
            util::anyhow::bail!("No segment IDs provided to use");
        } else if !self.segment_ids.is_empty() && self.all {
            util::anyhow::bail!("Cannot specify segment IDs and use all segments");
        }
        let override_object_id = self.override_object_id()?;

        let segment_ids = if self.all {
            match &override_object_id {
                Some(object_id) => global_store
                    .list_segment_ids(&[object_id.as_ref()], None)
                    .await?
                    .remove(0),
                None => global_store.list_segment_ids_global(None).await?,
            }
        } else {
            self.segment_ids.clone()
        };

        Ok(segment_ids)
    }

    pub async fn segment_id_to_object_id(
        &self,
        segment_ids: &[Uuid],
        global_store: &dyn GlobalStore,
    ) -> Result<HashMap<Uuid, FullObjectIdOwned>> {
        let override_object_id = self.override_object_id()?;

        let segment_id_to_object_id: HashMap<Uuid, FullObjectIdOwned> =
            if let Some(object_id) = override_object_id {
                segment_ids
                    .iter()
                    .map(|segment_id| (*segment_id, object_id.clone()))
                    .collect()
            } else {
                let liveness = global_store.query_segment_liveness(&segment_ids).await?;
                segment_ids
                    .iter()
                    .zip(liveness)
                    .map(|(segment_id, liveness)| (*segment_id, liveness.object_id))
                    .collect()
            };

        Ok(segment_id_to_object_id)
    }
}

#[derive(Debug, Clone, Default, Parser, Serialize, Deserialize)]
pub struct VacuumOptions {
    #[command(flatten)]
    #[serde(flatten)]
    pub object_id_args: VacuumObjectIdArgs,

    #[command(flatten)]
    #[serde(flatten)]
    pub background_vacuum_options: BackgroundVacuumOptions,

    #[arg(
        long,
        default_value_t = false,
        help = "If true, will return the number of planned deletes but not delete any files",
        env = "BRAINSTORE_VACUUM_DRY_RUN"
    )]
    #[serde(default)]
    pub vacuum_dry_run: bool,

    #[command(flatten)]
    #[serde(flatten)]
    pub vacuum_index_options: VacuumIndexOptions,
    // TODO(austin): Add options corresponding to other vacuum operations
    // here and use them when launching the vacuum worker.
}

#[derive(Debug, Clone, Parser, Serialize, Deserialize)]
pub struct BackgroundVacuumOptions {
    #[arg(
        long,
        help = "Seconds to sleep between loop iterations",
        env = "BRAINSTORE_BACKGROUND_VACUUM_SLEEP_SECONDS",
        default_value_t = default_background_vacuum_sleep_seconds(),
    )]
    pub background_vacuum_sleep_seconds: u64,

    #[arg(
        long,
        help = "Maximum number of loop iterations to run",
        env = "BRAINSTORE_BACKGROUND_VACUUM_MAX_ITERATIONS"
    )]
    pub background_vacuum_max_iterations: Option<u32>,
}

fn default_background_vacuum_sleep_seconds() -> u64 {
    10
}

impl Default for BackgroundVacuumOptions {
    fn default() -> Self {
        Self {
            background_vacuum_sleep_seconds: default_background_vacuum_sleep_seconds(),
            background_vacuum_max_iterations: None,
        }
    }
}

#[derive(Debug, Clone, Default, Parser, Serialize, Deserialize)]
pub struct VacuumObjectIdArgs {
    #[arg(help = "Object IDs to vacuum", env = "BRAINSTORE_VACUUM_OBJECT_IDS")]
    #[serde(default)]
    pub vacuum_object_ids: Vec<String>,

    #[arg(
        long,
        default_value_t = false,
        help = "Vacuum all objects",
        env = "BRAINSTORE_VACUUM_OBJECT_ALL"
    )]
    #[serde(default)]
    pub vacuum_all: bool,
}

pub fn parse_vacuum_object_id_args(
    args: VacuumObjectIdArgs,
) -> Result<Option<Vec<FullObjectIdOwned>>> {
    if args.vacuum_all && !args.vacuum_object_ids.is_empty() {
        return Err(anyhow!(
            "Cannot specify object IDs and also specify --vacuum-all"
        ));
    }
    let object_ids: Option<Vec<FullObjectIdOwned>> = if args.vacuum_all {
        None
    } else {
        Some(
            args.vacuum_object_ids
                .into_iter()
                .map(|s| s.parse::<FullObjectIdOwned>())
                .collect::<Result<_>>()?,
        )
    };
    Ok(object_ids)
}

#[derive(Debug, Clone, Default, Parser, Serialize, Deserialize)]
pub struct TimeBasedRetentionWorkerOptions {
    #[command(flatten)]
    #[serde(flatten)]
    pub object_id_args: TimeBasedRetentionObjectIdArgs,

    #[command(flatten)]
    #[serde(flatten)]
    pub background_time_based_retention_options: BackgroundTimeBasedRetentionOptions,

    #[arg(
        long,
        default_value_t = false,
        help = "If true, will return the number of planned deletes instead of deleting anything",
        env = "BRAINSTORE_TIME_BASED_RETENTION_DRY_RUN"
    )]
    #[serde(default)]
    pub time_based_retention_dry_run: bool,

    #[command(flatten)]
    #[serde(flatten)]
    pub time_based_retention_options: TimeBasedRetentionOptions,
}

#[derive(Debug, Clone, Parser, Serialize, Deserialize)]
pub struct BackgroundTimeBasedRetentionOptions {
    #[arg(
        long,
        help = "Seconds to sleep between loop iterations",
        env = "BRAINSTORE_BACKGROUND_TIME_BASED_RETENTION_SLEEP_SECONDS",
        default_value_t = default_background_time_based_retention_sleep_seconds(),
    )]
    pub background_time_based_retention_sleep_seconds: u64,

    #[arg(
        long,
        help = "Maximum number of loop iterations to run",
        env = "BRAINSTORE_BACKGROUND_TIME_BASED_RETENTION_MAX_ITERATIONS"
    )]
    pub background_time_based_retention_max_iterations: Option<u32>,
}

fn default_background_time_based_retention_sleep_seconds() -> u64 {
    10
}

impl Default for BackgroundTimeBasedRetentionOptions {
    fn default() -> Self {
        Self {
            background_time_based_retention_sleep_seconds:
                default_background_time_based_retention_sleep_seconds(),
            background_time_based_retention_max_iterations: None,
        }
    }
}

#[derive(Debug, Clone, Default, Parser, Serialize, Deserialize)]
pub struct TimeBasedRetentionObjectIdArgs {
    #[arg(
        long,
        help = "Object IDs to run time-based retention on",
        env = "BRAINSTORE_TIME_BASED_RETENTION_OBJECT_IDS",
        value_delimiter = ','
    )]
    #[serde(default)]
    pub time_based_retention_object_ids: Vec<String>,

    #[arg(
        long,
        default_value_t = false,
        help = "Run time-based retention on all objects",
        env = "BRAINSTORE_TIME_BASED_RETENTION_OBJECT_ALL"
    )]
    #[serde(default)]
    pub time_based_retention_all: bool,
}

pub fn parse_time_based_retention_object_id_args(
    args: &TimeBasedRetentionObjectIdArgs,
) -> Result<Option<Vec<FullObjectIdOwned>>> {
    if args.time_based_retention_all && !args.time_based_retention_object_ids.is_empty() {
        return Err(anyhow!("Cannot specify object IDs and also specify --all"));
    }
    let object_ids: Option<Vec<FullObjectIdOwned>> = if args.time_based_retention_all {
        None
    } else {
        Some(
            args.time_based_retention_object_ids
                .iter()
                .map(|s| s.parse::<FullObjectIdOwned>())
                .collect::<Result<_>>()?,
        )
    };
    Ok(object_ids)
}

#[derive(Debug, Clone)]
pub struct Process {
    pub pid: u64,
    pub cmd: String,
    pub args: serde_json::Value,
    pub start_time: std::time::Instant,
    pub end_time: Option<std::time::Instant>,
}

#[derive(Clone)]
pub struct ProcessList(Arc<ProcessListInner>);

struct ProcessListInner {
    processes: Mutex<HashMap<u64, Process>>,
    last_pid: AtomicU64,
}

impl ProcessList {
    fn new() -> Self {
        Self(Arc::new(ProcessListInner {
            processes: Mutex::new(HashMap::new()),
            last_pid: AtomicU64::new(0),
        }))
    }

    pub fn start(&self, cmd: &str, args: serde_json::Value) -> ProcessGuard {
        let pid = self
            .0
            .last_pid
            .fetch_add(1, std::sync::atomic::Ordering::Relaxed);
        self.0.processes.lock().unwrap().insert(
            pid,
            Process {
                pid,
                cmd: cmd.to_string(),
                args,
                start_time: std::time::Instant::now(),
                end_time: None,
            },
        );
        ProcessGuard {
            process_list: self.clone(),
            pid,
        }
    }

    pub fn end(&self, pid: u64) {
        self.0
            .processes
            .lock()
            .unwrap()
            .get_mut(&pid)
            .unwrap()
            .end_time = Some(std::time::Instant::now());
    }

    pub fn list(&self, only_active: bool) -> Vec<Process> {
        self.0
            .processes
            .lock()
            .unwrap()
            .values()
            .filter(|p| !only_active || p.end_time.is_none())
            .map(|p| p.clone())
            .collect()
    }
}

pub struct ProcessGuard {
    process_list: ProcessList,
    pid: u64,
}

impl Drop for ProcessGuard {
    fn drop(&mut self) {
        self.process_list.end(self.pid);
    }
}
