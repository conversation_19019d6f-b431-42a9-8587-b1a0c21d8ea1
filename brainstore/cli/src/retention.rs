use lazy_static::lazy_static;
use std::sync::Arc;
use tokio::sync::Mutex;

use crate::base::{
    self, parse_time_based_retention_object_id_args, AppState, CLIArgs,
    TimeBasedRetentionObjectIdArgs, TimeBasedRetentionWorkerOptions,
};
use crate::{http_client::get_http_client, pg_pool::get_pg_conn};
use clap::{Parser, Subcommand};
use serde::{Deserialize, Serialize};
use serde_json::json;
use storage::service_token::query_service_token;
use storage::{
    global_store::TimeBasedRetentionStats,
    retention_policy_lookup::ControlPlaneContext,
    retention_worker::{
        time_based_retention, time_based_retention_stateless, TimeBasedRetentionInput,
        TimeBasedRetentionOptionalInput, TimeBasedRetentionOptions,
    },
};
use tracing::instrument;
use util::{
    anyhow::{anyhow, Result},
    system_types::{FullObjectId, FullObjectIdOwned},
    url::Url,
};

#[derive(Subcommand, Debug, Clone, Serialize, Deserialize)]
pub enum RetentionCommands {
    TimeBasedRetention(CLIArgs<TimeBasedRetentionFullArgs>),
}

pub fn base_args(args: &RetentionCommands) -> &base::BaseArgs {
    match args {
        RetentionCommands::TimeBasedRetention(a) => &a.base,
    }
}

pub async fn main(args: RetentionCommands) -> Result<util::Value> {
    let app_state = AppState::new(base_args(&args))?;
    match args {
        RetentionCommands::TimeBasedRetention(a) => {
            run_time_based_retention(app_state, &a.args).await
        }
    }
}

#[derive(Debug, Clone, Default, Parser, Serialize, Deserialize)]
pub struct TimeBasedRetentionFullArgs {
    #[command(flatten)]
    #[serde(flatten)]
    pub args: TimeBasedRetentionArgs,

    #[command(flatten)]
    #[serde(flatten)]
    pub options: TimeBasedRetentionOptions,
}

#[derive(Debug, Clone, Default, Parser, Serialize, Deserialize)]
pub struct TimeBasedRetentionArgs {
    #[command(flatten)]
    #[serde(flatten)]
    pub object_id_args: TimeBasedRetentionObjectIdArgs,

    #[arg(long, default_value_t = false)]
    #[serde(default)]
    pub dry_run: bool,
}

#[derive(Debug, Clone, Default, Parser, Serialize, Deserialize)]
pub struct TimeBasedRetentionStatelessObjectIdArgs {
    #[arg(help = "Object IDs to run time-based retention on")]
    #[serde(default)]
    pub object_ids: Vec<String>,

    #[arg(long, help = "Run time-based retention on all objects")]
    #[serde(default)]
    pub all: bool,
}

#[derive(Parser, Debug, Clone, Serialize, Deserialize)]
pub struct TimeBasedRetentionStatelessArgs {
    #[command(flatten)]
    #[serde(flatten)]
    pub object_id_args: TimeBasedRetentionStatelessObjectIdArgs,

    #[arg(
        long,
        default_value_t = false,
        help = "Dry run, will return the number of planned deletes but not delete any files"
    )]
    #[serde(default)]
    pub dry_run: bool,
}

#[derive(Debug, Clone)]
pub struct TimeBasedRetentionStatelessFullArgs {
    pub args: TimeBasedRetentionStatelessArgs,
    pub options: TimeBasedRetentionOptions,
}

pub async fn run_time_based_retention(
    app_state: Arc<AppState>,
    full_args: &TimeBasedRetentionFullArgs,
) -> Result<util::Value> {
    let object_ids = parse_time_based_retention_object_id_args(&full_args.args.object_id_args)?;
    let object_ids_refs: Option<Vec<FullObjectId>> = object_ids
        .as_ref()
        .map(|ids| ids.iter().map(|id| id.as_ref()).collect());
    let object_ids_slice: Option<&[FullObjectId]> = object_ids_refs.as_deref();

    let app_url = app_state.base_args.app_url.parse::<Url>()?;
    let service_token = match get_service_token(app_state.clone()).await? {
        Some(api_key) => api_key,
        None => {
            return Err(anyhow!("No retention service token found"));
        }
    };

    let output = time_based_retention(
        TimeBasedRetentionInput {
            object_ids: object_ids_slice,
            global_store: app_state.config.global_store.clone(),
            index_store: &app_state.config.index,
            locks_manager: app_state.config.locks_manager.as_ref(),
            config_file_schema: app_state.config_schema.as_ref(),
            control_plane_ctx: Some(&ControlPlaneContext {
                http_client: get_http_client()?,
                app_url,
                service_token,
            }),
            dry_run: full_args.args.dry_run,
        },
        TimeBasedRetentionOptionalInput::default(),
        &full_args.options,
    )
    .await?;

    Ok(json!(output))
}

#[derive(Serialize, Debug, Default, Deserialize)]
pub struct TimeBasedRetentionStatelessOutput {
    pub success: bool,

    #[serde(flatten)]
    #[serde(default)]
    pub stats: TimeBasedRetentionStats,

    pub error: Option<String>,
}

#[instrument(err, skip(app_state))]
pub async fn run_time_based_retention_stateless(
    app_state: Arc<AppState>,
    full_args: TimeBasedRetentionStatelessFullArgs,
) -> Result<util::Value> {
    if full_args.args.object_id_args.all && !full_args.args.object_id_args.object_ids.is_empty() {
        util::anyhow::bail!("Cannot specify object IDs and also specify --all");
    }

    let object_ids: Option<Vec<FullObjectIdOwned>> = if full_args.args.object_id_args.all {
        None
    } else {
        Some(
            full_args
                .args
                .object_id_args
                .object_ids
                .into_iter()
                .map(|s| s.parse::<FullObjectIdOwned>())
                .collect::<Result<_>>()?,
        )
    };
    let object_ids_refs: Option<Vec<FullObjectId>> = object_ids
        .as_ref()
        .map(|ids| ids.iter().map(|id| id.as_ref()).collect());
    let object_ids_slice: Option<&[FullObjectId]> = object_ids_refs.as_deref();

    let app_url = app_state.base_args.app_url.parse::<Url>()?;
    let service_token = match get_service_token(app_state.clone()).await? {
        Some(api_key) => api_key,
        None => {
            return Err(anyhow!("No retention service token found"));
        }
    };

    let result = time_based_retention_stateless(
        TimeBasedRetentionInput {
            object_ids: object_ids_slice,
            global_store: app_state.config.global_store.clone(),
            index_store: &app_state.config.index,
            locks_manager: app_state.config.locks_manager.as_ref(),
            config_file_schema: app_state.config_schema.as_ref(),
            control_plane_ctx: Some(&ControlPlaneContext {
                http_client: get_http_client()?,
                app_url,
                service_token,
            }),
            dry_run: full_args.args.dry_run,
        },
        TimeBasedRetentionOptionalInput::default(),
        &full_args.options,
    )
    .await;

    let output = match result {
        Err(e) => TimeBasedRetentionStatelessOutput {
            error: Some(e.to_string()),
            ..Default::default()
        },
        Ok(stats) => TimeBasedRetentionStatelessOutput {
            success: true,
            stats,
            ..Default::default()
        },
    };

    Ok(json!(output))
}

pub async fn time_based_retention_worker_task(
    app_state: Arc<AppState>,
    args: TimeBasedRetentionWorkerOptions,
) {
    let full_args = TimeBasedRetentionFullArgs {
        args: TimeBasedRetentionArgs {
            object_id_args: args.object_id_args.clone(),
            dry_run: args.time_based_retention_dry_run,
        },
        options: args.time_based_retention_options.clone(),
    };

    log::info!("Launching time-based retention worker");

    let mut iterations = 0;
    loop {
        log::debug!("Running time-based retention loop (iter {})", iterations);

        match run_time_based_retention(app_state.clone(), &full_args).await {
            Ok(_) => {}
            Err(e) => {
                log::warn!("Error running time-based retention: {:?}", e);
            }
        };

        iterations += 1;

        if let Some(max_iterations) = args
            .background_time_based_retention_options
            .background_time_based_retention_max_iterations
        {
            if iterations >= max_iterations {
                log::info!(
                    "Reached maximum number of iterations ({}), exiting time-based retention loop",
                    max_iterations
                );
                break;
            }
        }

        log::debug!(
            "Sleeping for {} seconds before next time-based retention iteration",
            args.background_time_based_retention_options
                .background_time_based_retention_sleep_seconds
        );

        tokio::time::sleep(std::time::Duration::from_secs(
            args.background_time_based_retention_options
                .background_time_based_retention_sleep_seconds,
        ))
        .await;
    }
}

lazy_static! {
    static ref RETENTION_SERVICE_TOKEN: Mutex<Option<String>> = Mutex::new(None);
}

const RETENTION_SERVICE_TOKEN_NAME: &str = "bt_data_plane_service_token";
const USE_LAZY_SERVICE_TOKEN: bool = false;

pub async fn get_service_token(app_state: Arc<AppState>) -> Result<Option<String>> {
    if USE_LAZY_SERVICE_TOKEN {
        if let Some(token) = RETENTION_SERVICE_TOKEN.lock().await.as_ref() {
            return Ok(Some(token.clone()));
        }
    }

    let pg_conn = get_pg_conn(app_state.clone())?;
    let token = query_service_token(&pg_conn, RETENTION_SERVICE_TOKEN_NAME).await?;

    if USE_LAZY_SERVICE_TOKEN {
        if let Some(token) = &token {
            *RETENTION_SERVICE_TOKEN.lock().await = Some(token.clone());
        }
    }

    Ok(token)
}
