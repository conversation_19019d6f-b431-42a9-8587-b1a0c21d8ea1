use std::collections::HashMap;
use std::time::{Instant, SystemTime};
use std::{sync::Arc, time::Duration};

use actix_web::{
    App, HttpRequest, HttpResponse, HttpServer, Responder, get, post,
    web::{self, Bytes},
};
use agent::opentelemetry_sdk::metrics::Temporality;
use agent::{
    memprof::{MemprofHttpLabel, MemprofWriteRawRequest},
    opentelemetry_otlp::{WithExportConfig, WithHttpConfig},
};
use agent::{
    opentelemetry_otlp::MetricExporter,
    opentelemetry_sdk::metrics::{PeriodicReader, SdkMeterProvider},
};
use clap::Parser;
use lru::LruCache;
use opentelemetry::global;
use opentelemetry_proto::tonic::collector::{
    logs::v1::ExportLogsServiceRequest, metrics::v1::ExportMetricsServiceRequest,
    trace::v1::ExportTraceServiceRequest,
};
use prost::Message;
use serde::{Deserialize, Serialize};
use serde_json::{Value, json};
use tokio::sync::Mutex;

use util::{
    Result, anyhow, reqwest,
    reqwest::header::{HeaderMap, HeaderName, HeaderValue},
    url_util::{parse_headers_arg, url_join},
};

use crate::metrics::PulseMeters;
use crate::otel::{
    add_org_id_to_export_logs_request, add_org_id_to_export_metrics_request,
    add_org_id_to_export_traces_request,
};

#[derive(Debug, Clone, Parser, Serialize, Deserialize)]
pub struct WebCommand {
    #[arg(
        long,
        help = "The host to listen on",
        default_value = "0.0.0.0",
        env = "HEALTH_SERVER_HOST"
    )]
    pub host: String,

    #[arg(
        long,
        help = "The port to listen on",
        default_value = "4319",
        env = "HEALTH_SERVER_PORT"
    )]
    pub port: u16,

    #[arg(short, long, action = clap::ArgAction::Count, env = "HEALTH_SERVER_VERBOSE")]
    pub verbose: u8,

    #[arg(
        long,
        help = "The URL of the braintrust app to authorize requests",
        env = "BRAINTRUST_PROD_APP_URL",
        default_value = "https://www.braintrust.dev"
    )]
    pub prod_app_url: String,

    #[arg(
        long,
        help = "The interval in seconds to send metrics about pulse",
        env = "METRICS_INTERVAL_SECONDS",
        default_value_t = 30
    )]
    pub metrics_interval_seconds: u64,

    // Datadog docs: https://api.datadoghq.com/api/intake/otlp
    #[arg(
        long,
        help = "The OTLP endpoint to send metrics to",
        env = "OTLP_HTTP_ENDPOINT",
        default_value = "http://localhost:4318"
    )]
    pub otlp_http_endpoint: String,

    #[arg(
        long,
        help = "The headers to send with the OTLP request",
        env = "OTLP_HTTP_HEADERS",
        default_value = ""
    )]
    pub otlp_http_headers: String,

    #[arg(
        long,
        help = "The endpoint to send memory profiles to",
        env = "POLARSIGNALS_ENDPOINT",
        default_value = "https://api.polarsignals.com/api/parca/profilestore/profiles/writeraw"
    )]
    pub polarsignals_memprof_endpoint: String,

    #[arg(
        long,
        help = "The API key to use for the memory profiling service",
        env = "POLARSIGNALS_API_KEY",
        default_value = ""
    )]
    pub polarsignals_api_key: Option<String>,
}

#[derive(Clone, Debug)]
struct CachedAuthResult {
    org_id: String,
    org_name: Option<String>,
    cached_at: Instant,
}

const AUTH_CACHE_SIZE: usize = 1000;
const AUTH_CACHE_TTL_SECONDS: u64 = 300; // 5 minutes

#[derive(Clone, Debug)]
struct WebState {
    otlp_http_endpoint: String,
    otlp_http_headers: HeaderMap,
    prod_app_url: String,
    client: reqwest::Client,
    auth_cache: Arc<Mutex<LruCache<String, CachedAuthResult>>>,
    polarsignals_memprof_endpoint: String,
    polarsignals_api_key: Option<String>,
    meters: PulseMeters,
}

pub async fn main(args: WebCommand) -> Result<()> {
    let resource = agent::opentelemetry_sdk::Resource::builder_empty()
        .with_attribute(opentelemetry::KeyValue::new(
            "service.name",
            "pulse".to_string(),
        ))
        .build();

    let meter_provider = SdkMeterProvider::builder()
        .with_resource(resource)
        .with_reader(
            PeriodicReader::builder(
                MetricExporter::builder()
                    .with_http() // Use HTTP protocol
                    .with_endpoint(url_join(&args.otlp_http_endpoint, "/v1/metrics")) // Use same endpoint as spans
                    .with_headers(parse_headers_arg(&args.otlp_http_headers)?)
                    .with_timeout(Duration::from_secs(3))
                    .with_temporality(Temporality::Delta) // Required for Datadog
                    .build()?,
            )
            .with_interval(Duration::from_secs(args.metrics_interval_seconds))
            .build(),
        )
        .build();

    global::set_meter_provider(meter_provider.clone());

    // Parse the headers (comma separated key=value pairs)
    let mut otlp_http_headers = HeaderMap::new();
    for (key, value) in parse_headers_arg(&args.otlp_http_headers)? {
        otlp_http_headers.insert(
            HeaderName::from_bytes(key.as_bytes()).unwrap(),
            HeaderValue::from_bytes(value.as_bytes()).unwrap(),
        );
    }

    let state = Arc::new(WebState {
        otlp_http_endpoint: args.otlp_http_endpoint.clone(),
        otlp_http_headers,
        prod_app_url: args.prod_app_url,
        client: reqwest::Client::new(),
        auth_cache: Arc::new(Mutex::new(LruCache::new(
            std::num::NonZeroUsize::new(AUTH_CACHE_SIZE).unwrap(),
        ))),
        polarsignals_memprof_endpoint: args.polarsignals_memprof_endpoint,
        polarsignals_api_key: args.polarsignals_api_key,
        meters: PulseMeters::new(),
    });
    env_logger::Builder::from_default_env()
        .filter_level(match args.verbose {
            0 => log::LevelFilter::Warn,
            1 => log::LevelFilter::Info,
            _ => log::LevelFilter::Debug,
        })
        .init();

    HttpServer::new(move || {
        App::new()
            .app_data(web::Data::new(state.clone()))
            // 100mb payload limit
            .app_data(web::JsonConfig::default().limit(100 * 1024 * 1024))
            .app_data(web::PayloadConfig::default().limit(100 * 1024 * 1024))
            .service(ping)
            .service(status_request)
            .service(metrics)
            .service(traces)
            .service(logs)
            .service(memprof)
    })
    .bind((args.host, args.port))?
    .run()
    .await?;

    Ok(())
}

#[get("/")]
async fn ping() -> impl Responder {
    HttpResponse::Ok().body("pong")
}

#[derive(Deserialize)]
pub struct StatusRequest {
    pub resource: HashMap<String, String>,
    pub status: Value,
    pub config: Value,
}

#[post("/status")]
async fn status_request(
    req: HttpRequest,
    body: Bytes,
    state: web::Data<Arc<WebState>>,
) -> impl Responder {
    let auth_response = match authorize(&req, &state).await {
        Ok(auth) => auth,
        Err(e) => {
            log::error!("Failed to authorize: {}", e);
            return HttpResponse::Unauthorized().body(e.to_string());
        }
    };
    let org_id = auth_response.org_id.unwrap();
    let org_name = auth_response.org_name;
    state
        .meters
        .record_request("status", org_id.as_str(), body.len());
    log::info!("Received status request: {}", org_id);

    let json_payload = match serde_json::from_slice::<StatusRequest>(&body) {
        Ok(json_payload) => json_payload,
        Err(e) => {
            log::warn!("Failed to parse JSON status payload: {}", e);
            return HttpResponse::BadRequest().body(format!("Invalid JSON: {}", e));
        }
    };

    let now_nanos = SystemTime::now()
        .duration_since(SystemTime::UNIX_EPOCH)
        .unwrap()
        .as_nanos() as u64;

    let body: opentelemetry_proto::tonic::common::v1::AnyValue = json_to_any_value(&json!({
        "log_type": "status",
        "status": json_payload.status,
        "config": json_payload.config,
    }));

    let mut payload = ExportLogsServiceRequest {
        resource_logs: vec![opentelemetry_proto::tonic::logs::v1::ResourceLogs {
            schema_url: "".to_string(),
            resource: Some(opentelemetry_proto::tonic::resource::v1::Resource {
                attributes: json_payload
                    .resource
                    .iter()
                    .map(|(k, v)| opentelemetry_proto::tonic::common::v1::KeyValue {
                        key: k.into(),
                        value: Some(opentelemetry_proto::tonic::common::v1::AnyValue {
                            value: Some(opentelemetry_proto::tonic::common::v1::any_value::Value::StringValue(v.into())),
                        }),
                    })
                    .collect(),
                    dropped_attributes_count: 0,
                    entity_refs: vec![],
            }),
            scope_logs: vec![opentelemetry_proto::tonic::logs::v1::ScopeLogs {
                schema_url: "".to_string(),
                scope: None,
                log_records: vec![opentelemetry_proto::tonic::logs::v1::LogRecord { time_unix_nano: now_nanos, observed_time_unix_nano: now_nanos,
                    severity_number: 2,
                    severity_text: "".to_string(),
                     body: Some(body),
                     attributes: vec![],
                     dropped_attributes_count: 0,
                     flags: 0,
                     trace_id: vec![],
                     span_id: vec![],
                     event_name: "healthcheck".to_string() }],
            }],
        }],
    };

    add_org_id_to_export_logs_request(&mut payload, &org_id, org_name.as_deref());

    let mut header_map = state.otlp_http_headers.clone();
    header_map.insert(
        HeaderName::from_static("content-type"),
        HeaderValue::from_static("application/x-protobuf"),
    );
    let proto_body = payload.encode_to_vec();

    let response = state
        .client
        .post(url_join(&state.otlp_http_endpoint, "/v1/logs"))
        .headers(header_map)
        .body(proto_body)
        .send()
        .await;

    wrap_reqwests_response(response, "logs").await
}

#[post("/otel/v1/metrics")]
async fn metrics(req: HttpRequest, body: Bytes, state: web::Data<Arc<WebState>>) -> impl Responder {
    let auth_response = match authorize(&req, &state).await {
        Ok(auth) => auth,
        Err(e) => {
            log::error!("Failed to authorize: {}", e);
            return HttpResponse::Unauthorized().body(e.to_string());
        }
    };
    let org_id = auth_response.org_id.unwrap();
    let org_name = auth_response.org_name;
    log::info!("Received metrics request: {}", org_id);
    state
        .meters
        .record_request("metrics", org_id.as_str(), body.len());

    // Get content type from request headers
    let content_type = req
        .headers()
        .get("content-type")
        .and_then(|h| h.to_str().ok())
        .unwrap_or("");

    // Parse based on content type
    let mut payload = if content_type.contains("application/json") {
        // Parse as JSON
        match serde_json::from_slice::<ExportMetricsServiceRequest>(&body) {
            Ok(json_payload) => json_payload,
            Err(e) => {
                log::warn!("Failed to parse JSON metrics payload: {}", e);
                return HttpResponse::BadRequest().body(format!("Invalid JSON: {}", e));
            }
        }
    } else if content_type.contains("application/x-protobuf") {
        // Parse as protobuf
        match ExportMetricsServiceRequest::decode(&body[..]) {
            Ok(proto_payload) => {
                log::debug!("Received protobuf metrics payload:");
                log::debug!(
                    "  Number of resource metrics: {}",
                    proto_payload.resource_metrics.len()
                );

                proto_payload
            }
            Err(e) => {
                log::warn!("Failed to parse protobuf metrics payload: {}", e);
                return HttpResponse::BadRequest().body(format!("Invalid protobuf: {}", e));
            }
        }
    } else {
        log::warn!("Unsupported content type: {}", content_type);
        return HttpResponse::BadRequest()
            .body("Unsupported content type. Expected application/json or application/x-protobuf");
    };

    add_org_id_to_export_metrics_request(&mut payload, &org_id, org_name.as_deref());

    log::debug!("Received metrics payload: {:?}", payload);

    let mut header_map = state.otlp_http_headers.clone();
    header_map.insert(
        HeaderName::from_static("content-type"),
        HeaderValue::from_static("application/x-protobuf"),
    );
    let proto_body = payload.encode_to_vec();

    let response = state
        .client
        .post(url_join(&state.otlp_http_endpoint, "/v1/metrics"))
        .headers(header_map)
        .body(proto_body)
        .send()
        .await;

    wrap_reqwests_response(response, "metrics").await
}

#[post("/otel/v1/traces")]
async fn traces(req: HttpRequest, body: Bytes, state: web::Data<Arc<WebState>>) -> impl Responder {
    let auth_response = match authorize(&req, &state).await {
        Ok(auth) => auth,
        Err(e) => {
            log::error!("Failed to authorize: {}", e);
            return HttpResponse::Unauthorized().body(e.to_string());
        }
    };
    let org_id = auth_response.org_id.unwrap();
    let org_name = auth_response.org_name;
    log::info!("Received traces request: {}", org_id);
    state
        .meters
        .record_request("traces", org_id.as_str(), body.len());

    // Get content type from request headers
    let content_type = req
        .headers()
        .get("content-type")
        .and_then(|h| h.to_str().ok())
        .unwrap_or("");

    // Parse based on content type
    let mut payload = if content_type.contains("application/json") {
        // Parse as JSON
        match serde_json::from_slice::<ExportTraceServiceRequest>(&body) {
            Ok(json_payload) => json_payload,
            Err(e) => {
                log::warn!("Failed to parse JSON metrics payload: {}", e);
                return HttpResponse::BadRequest().body(format!("Invalid JSON: {}", e));
            }
        }
    } else if content_type.contains("application/x-protobuf") {
        // Parse as protobuf
        match ExportTraceServiceRequest::decode(&body[..]) {
            Ok(proto_payload) => {
                log::debug!("Received protobuf metrics payload:");

                proto_payload
            }
            Err(e) => {
                log::warn!("Failed to parse protobuf metrics payload: {}", e);
                return HttpResponse::BadRequest().body(format!("Invalid protobuf: {}", e));
            }
        }
    } else {
        log::warn!("Unsupported content type: {}", content_type);
        return HttpResponse::BadRequest()
            .body("Unsupported content type. Expected application/json or application/x-protobuf");
    };

    add_org_id_to_export_traces_request(&mut payload, &org_id, org_name.as_deref());

    log::debug!("Received traces payload: {:?}", payload);

    let mut header_map = state.otlp_http_headers.clone();
    header_map.insert(
        HeaderName::from_static("content-type"),
        HeaderValue::from_static("application/x-protobuf"),
    );
    let proto_body = payload.encode_to_vec();

    let response = state
        .client
        .post(url_join(&state.otlp_http_endpoint, "/v1/traces"))
        .headers(header_map)
        .body(proto_body)
        .send()
        .await;

    wrap_reqwests_response(response, "traces").await
}

#[post("/otel/v1/logs")]
async fn logs(req: HttpRequest, body: Bytes, state: web::Data<Arc<WebState>>) -> impl Responder {
    let auth_response = match authorize(&req, &state).await {
        Ok(auth) => auth,
        Err(e) => {
            log::error!("Failed to authorize: {}", e);
            return HttpResponse::Unauthorized().body(e.to_string());
        }
    };
    let org_id = auth_response.org_id.unwrap();
    let org_name = auth_response.org_name;
    state
        .meters
        .record_request("traces", org_id.as_str(), body.len());
    log::info!("Received logs request: {}", org_id);

    // Get content type from request headers
    let content_type = req
        .headers()
        .get("content-type")
        .and_then(|h| h.to_str().ok())
        .unwrap_or("");

    // Parse based on content type
    let mut payload = if content_type.contains("application/json") {
        // Parse as JSON
        match serde_json::from_slice::<ExportLogsServiceRequest>(&body) {
            Ok(json_payload) => json_payload,
            Err(e) => {
                log::warn!("Failed to parse JSON logs payload: {}", e);
                return HttpResponse::BadRequest().body(format!("Invalid JSON: {}", e));
            }
        }
    } else if content_type.contains("application/x-protobuf") {
        // Parse as protobuf
        match ExportLogsServiceRequest::decode(&body[..]) {
            Ok(proto_payload) => {
                log::debug!("Received protobuf logs payload:");

                proto_payload
            }
            Err(e) => {
                log::warn!("Failed to parse protobuf logs payload: {}", e);
                return HttpResponse::BadRequest().body(format!("Invalid protobuf: {}", e));
            }
        }
    } else {
        log::warn!("Unsupported content type: {}", content_type);
        return HttpResponse::BadRequest()
            .body("Unsupported content type. Expected application/json or application/x-protobuf");
    };

    add_org_id_to_export_logs_request(&mut payload, &org_id, org_name.as_deref());

    log::debug!("Received traces payload: {:?}", payload);

    let mut header_map = state.otlp_http_headers.clone();
    header_map.insert(
        HeaderName::from_static("content-type"),
        HeaderValue::from_static("application/x-protobuf"),
    );
    let proto_body = payload.encode_to_vec();

    let response = state
        .client
        .post(url_join(&state.otlp_http_endpoint, "/v1/logs"))
        .headers(header_map)
        .body(proto_body)
        .send()
        .await;

    wrap_reqwests_response(response, "logs").await
}

#[post("/prof/mem")]
async fn memprof(req: HttpRequest, body: Bytes, state: web::Data<Arc<WebState>>) -> impl Responder {
    let api_key = match state.polarsignals_api_key.as_ref() {
        Some(api_key) => api_key,
        None => {
            log::error!("No API key provided");
            return HttpResponse::InternalServerError()
                .body("No memory profiling API key provided");
        }
    };

    let auth_response = match authorize(&req, state.as_ref()).await {
        Ok(auth) => auth,
        Err(e) => {
            log::error!("Failed to authorize: {}", e);
            return HttpResponse::Unauthorized().body(e.to_string());
        }
    };
    let org_id = auth_response.org_id.unwrap();
    let org_name = auth_response.org_name;

    state
        .meters
        .record_request("memprof", org_id.as_str(), body.len());

    let mut request_body = match serde_json::from_slice::<MemprofWriteRawRequest>(&body) {
        Ok(request_body) => request_body,
        Err(e) => {
            log::warn!("Failed to parse JSON memprof payload: {}", e);
            return HttpResponse::BadRequest().body(format!("Invalid JSON: {}", e));
        }
    };

    for series in request_body.series.iter_mut() {
        series.labels.labels.push(MemprofHttpLabel {
            name: "bt_org_id".to_string(),
            value: org_id.clone(),
        });
        if let Some(name) = &org_name {
            series.labels.labels.push(MemprofHttpLabel {
                name: "bt_org_name".to_string(),
                value: name.clone(),
            });
        }
    }

    let response = state
        .client
        .post(&state.polarsignals_memprof_endpoint)
        .header("Authorization", format!("Bearer {}", api_key))
        .json(&request_body)
        .send()
        .await;

    wrap_reqwests_response(response, "memory profiling").await
}

pub async fn wrap_reqwests_response(
    response: Result<reqwest::Response, reqwest::Error>,
    service_name: &str,
) -> HttpResponse {
    match response {
        Ok(resp) => {
            let status = resp.status();
            if status.is_success() {
                // Return 202 Accepted
                HttpResponse::Accepted().finish()
            } else if status.as_u16() == 400 {
                // Pass through 400 Bad Request
                let error_body = resp
                    .text()
                    .await
                    .unwrap_or_else(|_| "Bad Request".to_string());
                log::error!(
                    "Downstream {} service returned 400 Bad Request: {}",
                    service_name,
                    error_body
                );
                HttpResponse::BadRequest().body(error_body)
            } else {
                // Any other error status becomes 500
                let error_body = resp
                    .text()
                    .await
                    .unwrap_or_else(|_| format!("Downstream error: {}", status));
                log::error!(
                    "Downstream returned error status {}: {}",
                    status,
                    error_body
                );
                HttpResponse::InternalServerError().body(format!(
                    "Failed to send {} downstream: {}",
                    service_name, error_body
                ))
            }
        }
        Err(e) => {
            // Network or other errors become 500
            log::error!("Failed to send {} downstream: {}", service_name, e);
            HttpResponse::InternalServerError()
                .body(format!("Failed to send {} downstream: {}", service_name, e))
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
struct AuthStateResponse {
    authorized: bool,
    org_id: Option<String>,
    org_name: Option<String>,
}

async fn authorize(req: &HttpRequest, state: &WebState) -> Result<AuthStateResponse> {
    let auth_header = match req.headers().get("authorization") {
        Some(auth_header) => auth_header,
        None => anyhow::bail!("No authorization header present"),
    };

    let (token_type, token) = match auth_header.to_str() {
        Ok(auth_str) => match auth_str.split_once(' ') {
            Some((token_type, token)) => (token_type, token),
            None => anyhow::bail!("Invalid authorization header format"),
        },
        Err(e) => anyhow::bail!("Invalid authorization header: {}", e),
    };

    if token_type != "Bearer" {
        anyhow::bail!("Invalid authorization header format");
    }

    {
        let mut cache = state.auth_cache.lock().await;
        if let Some(cached) = cache.get(token) {
            if cached.cached_at.elapsed().as_secs() < AUTH_CACHE_TTL_SECONDS {
                log::debug!("Cache hit for authorization token");
                return Ok(AuthStateResponse {
                    authorized: true,
                    org_id: Some(cached.org_id.clone()),
                    org_name: cached.org_name.clone(),
                });
            } else {
                // Expired!
                cache.pop(token);
            }
        }
    }

    log::debug!("Cache miss for authorization token, making API call");

    let response = state
        .client
        .post(url_join(&state.prod_app_url, "/api/brainstore/authorize"))
        .json(&json!({
            "nonce": "123", // TODO: we could validate the nonce the same way brainstore itself does
            "license": token,
            "version": 1,
        }))
        .send()
        .await;

    match response {
        Ok(resp) => {
            let status = resp.status();
            if status.is_success() {
                let auth_state: AuthStateResponse = resp.json().await?;
                if auth_state.authorized {
                    if let Some(org_id) = &auth_state.org_id {
                        {
                            let mut cache = state.auth_cache.lock().await;
                            cache.put(
                                token.to_string(),
                                CachedAuthResult {
                                    org_id: org_id.clone(),
                                    org_name: auth_state.org_name.clone(),
                                    cached_at: Instant::now(),
                                },
                            );
                        }
                        Ok(auth_state)
                    } else {
                        anyhow::bail!("Missing org_id")
                    }
                } else {
                    anyhow::bail!("Unauthorized")
                }
            } else {
                anyhow::bail!("[{}] {}", status, resp.text().await?);
            }
        }
        Err(e) => anyhow::bail!("Failed to authorize: {}", e),
    }
}

fn json_to_any_value(json: &serde_json::Value) -> opentelemetry_proto::tonic::common::v1::AnyValue {
    use opentelemetry_proto::tonic::common::v1::AnyValue;
    use opentelemetry_proto::tonic::common::v1::any_value::Value;
    use serde_json::Value as JsonValue;
    let value = match json {
        JsonValue::Null => Value::StringValue("null".into()), // or Value::Unset?
        JsonValue::Bool(b) => Value::BoolValue(*b),
        JsonValue::Number(n) => {
            if let Some(i) = n.as_i64() {
                Value::IntValue(i)
            } else if let Some(f) = n.as_f64() {
                Value::DoubleValue(f)
            } else {
                Value::StringValue(n.to_string()) // fallback
            }
        }
        JsonValue::String(s) => Value::StringValue(s.clone()),
        JsonValue::Array(arr) => {
            use opentelemetry_proto::tonic::common::v1::ArrayValue;
            Value::ArrayValue(ArrayValue {
                values: arr.iter().map(json_to_any_value).collect(),
            })
        }
        JsonValue::Object(map) => {
            use opentelemetry_proto::tonic::common::v1::KeyValue;
            use opentelemetry_proto::tonic::common::v1::KeyValueList;

            Value::KvlistValue(KeyValueList {
                values: map
                    .iter()
                    .map(|(k, v)| KeyValue {
                        key: k.clone(),
                        value: Some(json_to_any_value(v)),
                    })
                    .collect(),
            })
        }
    };

    AnyValue { value: Some(value) }
}
