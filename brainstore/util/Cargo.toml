[package]
name = "util"
version = "0.1.0"
edition = "2021"

[dependencies]
anyhow = { version = "1.0.89", features = ["backtrace"] }
async-stream = "0.3.6"
async-trait = "0.1.83"
arc-swap = "1.7.1"
byte-unit = "5.1.4"
chrono = { version = "0.4.38", features = ["serde"] }
clap = { version = "4.5.20", features = ["derive", "env"] }
futures = "0.3.30"
itertools = "0.13.0"
log = "0.4.22"
once_cell = "1.20.2"
ptree = "0.5.0"
serde = { version = "1.0.210", features = ["derive"] }
serde_json = { version = "1.0.128", default-features = false, features = ["std", "unbounded_depth"] }
serde_yaml = "0.9.34"
sysinfo = "0.35.1"
tokio = { version = "1.40.0", features = ["sync", "rt", "time", "macros"] }
tracing = { path = "../tracing" }
url = { version = "2.5.2", features = ["serde"] }
uuid = { version = "1.10.0", features = ["v4", "js"] }
ptree_derive = { path = "../ptree_derive" }
sha2 = "0.10.8"
bigjson = { path = "../bigjson" }
reqwest = { version = "0.12.7", default-features = false, features = ["blocking", "json", "rustls-tls"] }
