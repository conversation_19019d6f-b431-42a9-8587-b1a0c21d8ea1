use serde_json::json;

use util::{
    anyhow::Result,
    chrono::{DateTime, Duration, Utc},
    system_types::{FullObjectId, FullObjectIdOwned, ObjectId, ObjectType},
    test_util::assert_hashmap_eq,
    uuid::Uuid,
    xact::{PaginationKey, TransactionId},
};

use crate::{
    global_store::{TimeBasedRetentionCursor, TimeBasedRetentionState},
    process_wal::{compact_segment_wal, process_object_wal, ProcessObjectWalInput},
    retention_test::{run_test_with_global_stores, TestFixture},
    retention_worker::{
        time_based_retention, TimeBasedRetentionInput, TimeBasedRetentionOptionalInput,
        TimeBasedRetentionOptions,
    },
    test_util::make_compacted_wal_entries,
    wal_entry::WalEntry,
};

struct WalEntriesArgs<'a> {
    full_object_id: FullObjectIdOwned,
    ts: DateTime<Utc>,
    row_id_suffix: &'a str,
}

fn basic_wal_entries(args: WalEntriesArgs<'_>) -> Vec<WalEntry> {
    let timestamp = args.ts.timestamp() as u64;
    let xact_id_0 = TransactionId::from_timestamp(timestamp, 0);
    let xact_id_1 = TransactionId::from_timestamp(timestamp, 1);

    vec![
        WalEntry {
            _pagination_key: PaginationKey(0),
            _xact_id: xact_id_0,
            _object_type: args.full_object_id.object_type,
            _object_id: args.full_object_id.object_id.clone(),
            created: DateTime::<Utc>::from_timestamp_nanos(1000),
            _is_merge: Some(true),
            id: format!("row0-{}", args.row_id_suffix),
            data: json!({
                "field1": format!("value0-{}", args.row_id_suffix),
                "field2": 0,
            })
            .as_object()
            .unwrap()
            .clone(),
            ..Default::default()
        },
        WalEntry {
            _pagination_key: PaginationKey(1),
            _xact_id: xact_id_0,
            _object_type: args.full_object_id.object_type,
            _object_id: args.full_object_id.object_id.clone(),
            created: DateTime::<Utc>::from_timestamp_nanos(2000),
            id: format!("row1-{}", args.row_id_suffix),
            data: json!({
                "field1": format!("value1-{}", args.row_id_suffix),
                "field2": 1,
            })
            .as_object()
            .unwrap()
            .clone(),
            ..Default::default()
        },
        WalEntry {
            _pagination_key: PaginationKey(2),
            _xact_id: xact_id_1,
            _object_type: args.full_object_id.object_type,
            _object_id: args.full_object_id.object_id.clone(),
            created: DateTime::<Utc>::from_timestamp_nanos(3000),
            _is_merge: Some(true),
            id: format!("row1-{}", args.row_id_suffix),
            data: json!({
                "field1": format!("new-value1-{}", args.row_id_suffix),
            })
            .as_object()
            .unwrap()
            .clone(),
            ..Default::default()
        },
        WalEntry {
            _pagination_key: PaginationKey(3),
            _xact_id: xact_id_1,
            _object_type: args.full_object_id.object_type,
            _object_id: args.full_object_id.object_id,
            created: DateTime::<Utc>::from_timestamp_nanos(4000),
            _is_merge: Some(true),
            id: format!("row2-{}", args.row_id_suffix),
            data: json!({
                "field1": format!("value2-{}", args.row_id_suffix),
                "field2": 2,
            })
            .as_object()
            .unwrap()
            .clone(),
            ..Default::default()
        },
    ]
}

fn compacted_wal_entries(args: WalEntriesArgs<'_>) -> Vec<WalEntry> {
    let timestamp = args.ts.timestamp() as u64;
    let xact_id_0 = TransactionId::from_timestamp(timestamp, 0);
    let xact_id_1 = TransactionId::from_timestamp(timestamp, 1);

    vec![
        WalEntry {
            _pagination_key: PaginationKey(0),
            _xact_id: xact_id_0,
            _object_type: args.full_object_id.object_type,
            _object_id: args.full_object_id.object_id.clone(),
            created: DateTime::<Utc>::from_timestamp_nanos(1000),
            id: format!("row0-{}", args.row_id_suffix),
            data: json!({
                "field1": format!("value0-{}", args.row_id_suffix),
                "field2": 0,
            })
            .as_object()
            .unwrap()
            .clone(),
            ..Default::default()
        },
        WalEntry {
            _pagination_key: PaginationKey(1),
            _xact_id: xact_id_1,
            _object_type: args.full_object_id.object_type,
            _object_id: args.full_object_id.object_id.clone(),
            created: DateTime::<Utc>::from_timestamp_nanos(2000),
            id: format!("row1-{}", args.row_id_suffix),
            data: json!({
                "field1": format!("new-value1-{}", args.row_id_suffix),
                "field2": 1,
            })
            .as_object()
            .unwrap()
            .clone(),
            ..Default::default()
        },
        WalEntry {
            _pagination_key: PaginationKey(3),
            _xact_id: xact_id_1,
            _object_type: args.full_object_id.object_type,
            _object_id: args.full_object_id.object_id,
            created: DateTime::<Utc>::from_timestamp_nanos(4000),
            id: format!("row2-{}", args.row_id_suffix),
            data: json!({
                "field1": format!("value2-{}", args.row_id_suffix),
                "field2": 2,
            })
            .as_object()
            .unwrap()
            .clone(),
            ..Default::default()
        },
    ]
}

#[tokio::test]
async fn test_time_based_retention_worker() -> Result<()> {
    run_test_with_global_stores(test_time_based_retention_worker_inner).await
}

async fn test_time_based_retention_worker_inner(use_postgres_global_store: bool) -> Result<()> {
    let fixture = TestFixture::new(use_postgres_global_store).await;

    let object_id = FullObjectId::default();
    let segment_id = Uuid::new_v4();
    fixture
        .initialize_segment_metadata_in_object(object_id, segment_id)
        .await;

    // Create WAL entries with timestamps 31 and 29 days ago
    let now = Utc::now();
    let old_ts = now - Duration::days(31);
    let new_ts = now - Duration::days(29);
    let old_entries = basic_wal_entries(WalEntriesArgs {
        full_object_id: object_id.to_owned(),
        ts: old_ts,
        row_id_suffix: "31d",
    });
    let new_entries = basic_wal_entries(WalEntriesArgs {
        full_object_id: object_id.to_owned(),
        ts: new_ts,
        row_id_suffix: "29d",
    });
    fixture.write_wal_to_segment(segment_id, old_entries).await;
    fixture.write_wal_to_segment(segment_id, new_entries).await;
    process_object_wal(
        fixture.basic_fixture.process_wal_input(),
        Default::default(),
        Default::default(),
    )
    .await?;
    compact_segment_wal(
        fixture.compact_wal_input(segment_id),
        Default::default(),
        Default::default(),
    )
    .await?;

    let wal_entries = fixture.read_segment_wal_entries(segment_id).await;
    assert_eq!(wal_entries.len(), 4); // 4 transactions (2 per batch) with 2 entries each
    for wal_entry in wal_entries.iter() {
        assert_eq!(wal_entry.1.len(), 2);
    }
    let mut expected_compacted_wal_entries = compacted_wal_entries(WalEntriesArgs {
        full_object_id: object_id.to_owned(),
        ts: old_ts,
        row_id_suffix: "31d",
    });
    expected_compacted_wal_entries.extend(compacted_wal_entries(WalEntriesArgs {
        full_object_id: object_id.to_owned(),
        ts: new_ts,
        row_id_suffix: "29d",
    }));
    let expected_docs = make_compacted_wal_entries(expected_compacted_wal_entries);
    let docs = fixture.read_segment_docs(segment_id).await;
    assert_eq!(docs.len(), 6);
    assert_hashmap_eq(&docs, &expected_docs);

    // Run retention with 32 day policy. Nothing is more than 32 days old so the WAL and index
    // should not be affected.
    let output = time_based_retention(
        TimeBasedRetentionInput {
            object_ids: Some(&[object_id]),
            global_store: fixture.config().global_store.clone(),
            index_store: &fixture.config().index,
            locks_manager: &*fixture.config().locks_manager,
            config_file_schema: Some(&fixture.make_full_schema()),
            control_plane_ctx: None,
            dry_run: false,
        },
        TimeBasedRetentionOptionalInput {
            testing_object_id_to_retention_days: Some([(object_id, 32)].into_iter().collect()),
            ..Default::default()
        },
        &TimeBasedRetentionOptions::default(),
    )
    .await?;
    assert_eq!(output.num_processed_objects, 1);
    assert_eq!(output.num_processed_segments, 1);
    assert_eq!(output.segment_stats.wal_stats.num_deleted_wal_entries, 0);
    assert_eq!(output.segment_stats.index_stats.num_deleted_index_docs, 0);
    assert_eq!(output.segment_stats.index_stats.num_write_locks, 0);
    let wal_entries = fixture.read_segment_wal_entries(segment_id).await;
    assert_eq!(wal_entries.len(), 4);
    for wal_entry in wal_entries.iter() {
        assert_eq!(wal_entry.1.len(), 2);
    }
    assert_eq!(wal_entries[0].1[0].id, "row0-31d");
    assert_eq!(wal_entries[0].1[1].id, "row1-31d");
    assert_eq!(wal_entries[1].1[0].id, "row1-31d");
    assert_eq!(wal_entries[1].1[1].id, "row2-31d");
    let docs = fixture.read_segment_docs(segment_id).await;
    assert_eq!(docs.len(), 6);
    assert_hashmap_eq(&docs, &expected_docs);

    // Apply a 30-day retention policy. Because retention just ran, the worker shouldn't
    // wake up again just yet.
    let output = time_based_retention(
        TimeBasedRetentionInput {
            object_ids: Some(&[object_id]),
            global_store: fixture.config().global_store.clone(),
            index_store: &fixture.config().index,
            locks_manager: &*fixture.config().locks_manager,
            config_file_schema: Some(&fixture.make_full_schema()),
            control_plane_ctx: None,
            dry_run: false,
        },
        TimeBasedRetentionOptionalInput {
            testing_object_id_to_retention_days: Some([(object_id, 30)].into_iter().collect()),
            ..Default::default()
        },
        &TimeBasedRetentionOptions::default(),
    )
    .await?;
    assert_eq!(output.num_processed_objects, 0);
    assert_eq!(output.num_processed_segments, 0);
    assert_eq!(output.segment_stats.wal_stats.num_deleted_wal_entries, 0);
    assert_eq!(output.segment_stats.index_stats.num_deleted_index_docs, 0);
    assert_eq!(output.segment_stats.index_stats.num_write_locks, 0);
    let wal_entries = fixture.read_segment_wal_entries(segment_id).await;
    assert_eq!(wal_entries.len(), 4);
    for wal_entry in wal_entries.iter() {
        assert_eq!(wal_entry.1.len(), 2);
    }
    assert_eq!(wal_entries[0].1[0].id, "row0-31d");
    assert_eq!(wal_entries[0].1[1].id, "row1-31d");
    assert_eq!(wal_entries[1].1[0].id, "row1-31d");
    assert_eq!(wal_entries[1].1[1].id, "row2-31d");
    let docs = fixture.read_segment_docs(segment_id).await;
    assert_eq!(docs.len(), 6);
    assert_hashmap_eq(&docs, &expected_docs);

    // Now run with a zero interval for the retention worker, but do a dry run.
    // We should see the correct number of planned deletes but nothing should
    // actually be deleted.
    let output = time_based_retention(
        TimeBasedRetentionInput {
            object_ids: Some(&[object_id]),
            global_store: fixture.config().global_store.clone(),
            index_store: &fixture.config().index,
            locks_manager: &*fixture.config().locks_manager,
            config_file_schema: Some(&fixture.make_full_schema()),
            control_plane_ctx: None,
            dry_run: true,
        },
        TimeBasedRetentionOptionalInput {
            testing_object_id_to_retention_days: Some([(object_id, 30)].into_iter().collect()),
            ..Default::default()
        },
        &TimeBasedRetentionOptions {
            time_based_retention_interval_seconds: 0,
            ..Default::default()
        },
    )
    .await?;
    assert_eq!(output.num_processed_objects, 1);
    assert_eq!(output.num_processed_segments, 1);
    assert_eq!(output.segment_stats.wal_stats.num_deleted_wal_entries, 0);
    assert_eq!(output.segment_stats.index_stats.num_deleted_index_docs, 0);
    assert_eq!(
        output
            .segment_stats
            .wal_stats
            .planned_num_deleted_wal_entries,
        2
    );
    assert_eq!(
        output
            .segment_stats
            .index_stats
            .planned_num_deleted_index_docs,
        3
    );
    assert_eq!(output.segment_stats.index_stats.num_write_locks, 0);
    let wal_entries = fixture.read_segment_wal_entries(segment_id).await;
    assert_eq!(wal_entries.len(), 4);
    for wal_entry in wal_entries.iter() {
        assert_eq!(wal_entry.1.len(), 2);
    }

    // Now do a real run with a zero interval for the retention worker. The 30 day
    // old rows should be deleted out of the segment WAL as well as the tantivy index.
    let output = time_based_retention(
        TimeBasedRetentionInput {
            object_ids: Some(&[object_id]),
            global_store: fixture.config().global_store.clone(),
            index_store: &fixture.config().index,
            locks_manager: &*fixture.config().locks_manager,
            config_file_schema: Some(&fixture.make_full_schema()),
            control_plane_ctx: None,
            dry_run: false,
        },
        TimeBasedRetentionOptionalInput {
            testing_object_id_to_retention_days: Some([(object_id, 30)].into_iter().collect()),
            ..Default::default()
        },
        &TimeBasedRetentionOptions {
            time_based_retention_interval_seconds: 0,
            ..Default::default()
        },
    )
    .await?;
    assert_eq!(output.num_processed_objects, 1);
    assert_eq!(output.num_processed_segments, 1);
    assert_eq!(output.segment_stats.wal_stats.num_deleted_wal_entries, 2);
    assert_eq!(output.segment_stats.index_stats.num_deleted_index_docs, 3);
    assert_eq!(
        output
            .segment_stats
            .wal_stats
            .planned_num_deleted_wal_entries,
        2
    );
    assert_eq!(
        output
            .segment_stats
            .index_stats
            .planned_num_deleted_index_docs,
        3
    );
    assert_eq!(output.segment_stats.index_stats.num_write_locks, 1);
    let wal_entries = fixture.read_segment_wal_entries(segment_id).await;
    assert_eq!(wal_entries.len(), 2); // Only 29 day old entries remain
    for wal_entry in wal_entries.iter() {
        assert_eq!(wal_entry.1.len(), 2);
    }
    assert_eq!(wal_entries[0].1[0].id, "row0-29d");
    assert_eq!(wal_entries[0].1[1].id, "row1-29d");
    assert_eq!(wal_entries[1].1[0].id, "row1-29d");
    assert_eq!(wal_entries[1].1[1].id, "row2-29d");
    let expected_compacted_wal_entries = compacted_wal_entries(WalEntriesArgs {
        full_object_id: object_id.to_owned(),
        ts: new_ts,
        row_id_suffix: "29d",
    });
    let expected_docs = make_compacted_wal_entries(expected_compacted_wal_entries);
    let docs = fixture.read_segment_docs(segment_id).await;
    assert_eq!(docs.len(), 3);
    assert_hashmap_eq(&docs, &expected_docs);

    // Now apply a 28-day retention policy. All rows should be deleted from the WAL
    // and tantivy index.
    let output = time_based_retention(
        TimeBasedRetentionInput {
            object_ids: Some(&[object_id]),
            global_store: fixture.config().global_store.clone(),
            index_store: &fixture.config().index,
            locks_manager: &*fixture.config().locks_manager,
            config_file_schema: Some(&fixture.make_full_schema()),
            control_plane_ctx: None,
            dry_run: false,
        },
        TimeBasedRetentionOptionalInput {
            testing_object_id_to_retention_days: Some([(object_id, 28)].into_iter().collect()),
            ..Default::default()
        },
        &TimeBasedRetentionOptions {
            time_based_retention_interval_seconds: 0,
            ..Default::default()
        },
    )
    .await?;

    assert_eq!(output.num_processed_objects, 1);
    assert_eq!(output.num_processed_segments, 1);
    assert_eq!(output.segment_stats.wal_stats.num_deleted_wal_entries, 2);
    assert_eq!(output.segment_stats.index_stats.num_deleted_index_docs, 3);
    assert_eq!(output.segment_stats.index_stats.num_write_locks, 1);
    let wal_entries = fixture.read_segment_wal_entries(segment_id).await;
    assert!(wal_entries.is_empty());

    let docs = fixture.read_segment_docs(segment_id).await;
    assert!(docs.is_empty());

    Ok(())
}

#[tokio::test]
async fn test_time_based_retention_cursor() -> Result<()> {
    run_test_with_global_stores(test_time_based_retention_cursor_inner).await
}

async fn test_time_based_retention_cursor_inner(use_postgres_global_store: bool) -> Result<()> {
    let fixture = TestFixture::new(use_postgres_global_store).await;

    // Create three objects with segments
    let object_id1 = FullObjectId {
        object_type: ObjectType::Experiment,
        object_id: ObjectId::new("obj1").unwrap(),
    };
    let object_id2 = FullObjectId {
        object_type: ObjectType::Experiment,
        object_id: ObjectId::new("obj2").unwrap(),
    };
    let object_id3 = FullObjectId {
        object_type: ObjectType::Experiment,
        object_id: ObjectId::new("obj3").unwrap(),
    };

    let mut segment_ids = vec![
        Uuid::new_v4(),
        Uuid::new_v4(),
        Uuid::new_v4(),
        Uuid::new_v4(),
        Uuid::new_v4(),
    ];
    segment_ids.sort();
    let [segment_id1, segment_id2, segment_id3, segment_id4, segment_id5] =
        segment_ids.try_into().unwrap();

    // Initialize segments for each object
    fixture
        .initialize_segment_metadata_in_object(object_id1, segment_id1)
        .await;
    fixture
        .initialize_segment_metadata_in_object(object_id2, segment_id2)
        .await;
    fixture
        .initialize_segment_metadata_in_object(object_id2, segment_id3)
        .await;
    fixture
        .initialize_segment_metadata_in_object(object_id3, segment_id4)
        .await;
    fixture
        .initialize_segment_metadata_in_object(object_id3, segment_id5)
        .await;

    // Add some old data to all segments
    let now = Utc::now();
    let thirty_one_days_ago = now - Duration::days(31);

    let entries1 = basic_wal_entries(WalEntriesArgs {
        full_object_id: object_id1.to_owned(),
        ts: thirty_one_days_ago,
        row_id_suffix: "1",
    });
    let entries2 = basic_wal_entries(WalEntriesArgs {
        full_object_id: object_id2.to_owned(),
        ts: thirty_one_days_ago,
        row_id_suffix: "2",
    });
    let entries3 = basic_wal_entries(WalEntriesArgs {
        full_object_id: object_id2.to_owned(),
        ts: thirty_one_days_ago,
        row_id_suffix: "3",
    });
    let entries4 = basic_wal_entries(WalEntriesArgs {
        full_object_id: object_id3.to_owned(),
        ts: thirty_one_days_ago,
        row_id_suffix: "4",
    });
    let entries5 = basic_wal_entries(WalEntriesArgs {
        full_object_id: object_id3.to_owned(),
        ts: thirty_one_days_ago,
        row_id_suffix: "5",
    });

    fixture.write_wal_to_segment(segment_id1, entries1).await;
    fixture.write_wal_to_segment(segment_id2, entries2).await;
    fixture.write_wal_to_segment(segment_id3, entries3).await;
    fixture.write_wal_to_segment(segment_id4, entries4).await;
    fixture.write_wal_to_segment(segment_id5, entries5).await;

    for object_id in [object_id1, object_id2, object_id3] {
        process_object_wal(
            ProcessObjectWalInput {
                object_id: object_id,
                config: &fixture.config(),
            },
            Default::default(),
            Default::default(),
        )
        .await?;
    }

    for segment_id in [
        segment_id1,
        segment_id2,
        segment_id3,
        segment_id4,
        segment_id5,
    ] {
        compact_segment_wal(
            fixture.compact_wal_input(segment_id),
            Default::default(),
            Default::default(),
        )
        .await?;
    }

    // Force an error on object_id1 with a batch size of 1. The cursor shouldn't
    // move since we didn't complete any objects.
    let output = time_based_retention(
        TimeBasedRetentionInput {
            object_ids: Some(&[object_id1, object_id2, object_id3]),
            global_store: fixture.config().global_store.clone(),
            index_store: &fixture.config().index,
            locks_manager: &*fixture.config().locks_manager,
            config_file_schema: Some(&fixture.make_full_schema()),
            control_plane_ctx: None,
            dry_run: false,
        },
        TimeBasedRetentionOptionalInput {
            testing_object_id_to_retention_days: Some(
                [(object_id1, 30), (object_id2, 30), (object_id3, 30)]
                    .into_iter()
                    .collect(),
            ),
            testing_force_error_for_object_id: Some(object_id1),
            testing_force_error_for_segment_id: None,
        },
        &TimeBasedRetentionOptions {
            time_based_retention_object_batch_size: 1,
            time_based_retention_segment_batch_size: 1,
            ..Default::default()
        },
    )
    .await;
    assert!(output.is_err());
    let state = fixture
        .config()
        .global_store
        .query_time_based_retention_state()
        .await?;
    assert!(state.last_successful_start_ts.is_none());
    assert!(state.cursor.is_none());
    assert!(state.current_op_start_ts.is_some());
    let current_op_start_ts = state.current_op_start_ts.unwrap();
    assert!(state.operation.error.is_some());

    // Force an error on object_id2 when using an object batch size of 2. Since we
    // still made it past object_id1, the cursor should move to (object_id1, nil).
    let output = time_based_retention(
        TimeBasedRetentionInput {
            object_ids: Some(&[object_id1, object_id2, object_id3]),
            global_store: fixture.config().global_store.clone(),
            index_store: &fixture.config().index,
            locks_manager: &*fixture.config().locks_manager,
            config_file_schema: Some(&fixture.make_full_schema()),
            control_plane_ctx: None,
            dry_run: false,
        },
        TimeBasedRetentionOptionalInput {
            testing_object_id_to_retention_days: Some(
                [(object_id1, 1), (object_id2, 7), (object_id3, 30)]
                    .into_iter()
                    .collect(),
            ),
            testing_force_error_for_object_id: Some(object_id2),
            testing_force_error_for_segment_id: None,
        },
        &TimeBasedRetentionOptions {
            time_based_retention_object_batch_size: 2,
            time_based_retention_segment_batch_size: 1,
            ..Default::default()
        },
    )
    .await;
    assert!(output.is_err());
    let state = fixture
        .config()
        .global_store
        .query_time_based_retention_state()
        .await?;
    assert_eq!(
        state.cursor,
        Some(TimeBasedRetentionCursor {
            object_id: object_id1.to_owned(),
            segment_id: segment_id1,
        })
    );
    assert!(state.last_successful_start_ts.is_none());
    assert_eq!(state.current_op_start_ts, Some(current_op_start_ts));
    assert!(state.operation.error.is_some());

    // Now run the retention loop to completion.
    let output = time_based_retention(
        TimeBasedRetentionInput {
            object_ids: Some(&[object_id1, object_id2, object_id3]),
            global_store: fixture.config().global_store.clone(),
            index_store: &fixture.config().index,
            locks_manager: &*fixture.config().locks_manager,
            config_file_schema: Some(&fixture.make_full_schema()),
            control_plane_ctx: None,
            dry_run: false,
        },
        TimeBasedRetentionOptionalInput {
            testing_object_id_to_retention_days: Some(
                [(object_id1, 30), (object_id2, 60), (object_id3, 90)]
                    .into_iter()
                    .collect(),
            ),
            testing_force_error_for_object_id: None,
            testing_force_error_for_segment_id: None,
        },
        &TimeBasedRetentionOptions {
            time_based_retention_object_batch_size: 1,
            time_based_retention_segment_batch_size: 1,
            ..Default::default()
        },
    )
    .await;
    assert!(output.is_ok());
    let state = fixture
        .config()
        .global_store
        .query_time_based_retention_state()
        .await?;
    assert_eq!(state.last_successful_start_ts, Some(current_op_start_ts));
    let last_successful_start_ts = state.last_successful_start_ts;
    // After completion, current_op_start_ts should be set to null.
    assert!(state.current_op_start_ts.is_none());
    assert!(state.operation.completed_ts.is_some());

    // Reset the time-based retention state to prepare for the next batch of tests,
    // but leave last_successful_start_ts intact.
    fixture
        .config()
        .global_store
        .upsert_time_based_retention_state(&TimeBasedRetentionState {
            last_successful_start_ts,
            ..Default::default()
        })
        .await?;

    let before_start_ts = Utc::now();

    // Force an error at the segment level for segment_id3 when using a batch size of 2.
    // The cursor should advance to (object_id1, segment_id1) since the first object completed.
    let output = time_based_retention(
        TimeBasedRetentionInput {
            object_ids: None,
            global_store: fixture.config().global_store.clone(),
            index_store: &fixture.config().index,
            locks_manager: &*fixture.config().locks_manager,
            config_file_schema: Some(&fixture.make_full_schema()),
            control_plane_ctx: None,
            dry_run: false,
        },
        TimeBasedRetentionOptionalInput {
            testing_object_id_to_retention_days: Some(
                [(object_id1, 30), (object_id2, 30), (object_id3, 30)]
                    .into_iter()
                    .collect(),
            ),
            testing_force_error_for_object_id: None,
            testing_force_error_for_segment_id: Some(segment_id3),
        },
        &TimeBasedRetentionOptions {
            time_based_retention_object_batch_size: 1,
            time_based_retention_segment_batch_size: 2,
            // Set the interval to 0 to run the retention loop immediately.
            time_based_retention_interval_seconds: 0,
            ..Default::default()
        },
    )
    .await;
    assert!(output.is_err());
    let state = fixture
        .config()
        .global_store
        .query_time_based_retention_state()
        .await?;
    assert_eq!(
        state.cursor,
        Some(TimeBasedRetentionCursor {
            object_id: object_id2.to_owned(),
            segment_id: segment_id2,
        })
    );
    assert_eq!(state.last_successful_start_ts, last_successful_start_ts);
    assert!(state.operation.error.is_some());
    assert!(state.operation.completed_ts.is_none());

    // Mark the current_op_start_ts of this failed run. It should remain the current_op_start_ts until the run is fully completed.
    let current_op_start_ts = state.current_op_start_ts;
    assert!(current_op_start_ts.is_some());
    assert!(current_op_start_ts.unwrap() > before_start_ts);

    // Force an error for segment_id5 but use a batch size of 1.
    // The cursor should advance to (object_id3, segment_id4) since we processed object_id2 completely
    // and started on object_id3.
    let output = time_based_retention(
        TimeBasedRetentionInput {
            object_ids: None,
            global_store: fixture.config().global_store.clone(),
            index_store: &fixture.config().index,
            locks_manager: &*fixture.config().locks_manager,
            config_file_schema: Some(&fixture.make_full_schema()),
            control_plane_ctx: None,
            dry_run: false,
        },
        TimeBasedRetentionOptionalInput {
            testing_object_id_to_retention_days: Some(
                [(object_id1, 30), (object_id2, 30), (object_id3, 30)]
                    .into_iter()
                    .collect(),
            ),
            testing_force_error_for_object_id: None,
            testing_force_error_for_segment_id: Some(segment_id5),
        },
        &TimeBasedRetentionOptions {
            time_based_retention_object_batch_size: 1,
            time_based_retention_segment_batch_size: 1,
            ..Default::default()
        },
    )
    .await;
    assert!(output.is_err());
    let state = fixture
        .config()
        .global_store
        .query_time_based_retention_state()
        .await?;
    assert_eq!(
        state.cursor,
        Some(TimeBasedRetentionCursor {
            object_id: object_id3.to_owned(),
            segment_id: segment_id4,
        })
    );
    assert_eq!(state.last_successful_start_ts, last_successful_start_ts);
    assert_eq!(state.current_op_start_ts, current_op_start_ts);

    let before_complete_ts = Utc::now();

    // Complete the remaining objects and test that the cursor advances
    // to the end and the state is marked complete.
    let output = time_based_retention(
        TimeBasedRetentionInput {
            object_ids: Some(&[object_id1, object_id2, object_id3]),
            global_store: fixture.config().global_store.clone(),
            index_store: &fixture.config().index,
            locks_manager: &*fixture.config().locks_manager,
            config_file_schema: Some(&fixture.make_full_schema()),
            control_plane_ctx: None,
            dry_run: false,
        },
        TimeBasedRetentionOptionalInput {
            testing_object_id_to_retention_days: Some(
                [(object_id1, 30), (object_id2, 30), (object_id3, 30)]
                    .into_iter()
                    .collect(),
            ),
            testing_force_error_for_object_id: None,
            testing_force_error_for_segment_id: None,
        },
        &TimeBasedRetentionOptions {
            time_based_retention_object_batch_size: 10,
            time_based_retention_segment_batch_size: 10,
            ..Default::default()
        },
    )
    .await?;
    assert_eq!(output.num_processed_objects, 3); // All objects have now been processed.
    assert_eq!(output.num_processed_segments, 5); // All segments have now been processed.
    let state = fixture
        .config()
        .global_store
        .query_time_based_retention_state()
        .await?;
    // The last_successful_start_ts should now be updated.
    assert_eq!(state.last_successful_start_ts, current_op_start_ts);
    assert!(state.current_op_start_ts.is_none());
    // The completed_ts should have been marked during the last run.
    assert!(state.operation.completed_ts.unwrap() > before_complete_ts);
    // The most recent error should have been cleared by the successful iteration.
    assert!(state.operation.error.is_none());

    // Verify all segment WALs and tantivy indices are empty.
    for segment_id in [
        segment_id1,
        segment_id2,
        segment_id3,
        segment_id4,
        segment_id5,
    ] {
        let wal_entries = fixture.read_segment_wal_entries(segment_id).await;
        assert!(wal_entries.is_empty());

        let docs = fixture.read_segment_docs(segment_id).await;
        assert!(docs.is_empty());
    }

    Ok(())
}
