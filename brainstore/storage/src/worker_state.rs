use crate::{
    global_store::{GlobalStore, TimeBasedRetentionState},
    postgres_global_store::{SEGMENT_ID_TO_LIVENESS_TABLE, SEGMENT_ID_TO_TASK_INFO_TABLE},
    postgres_pool::PostgresPool,
};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use tokio_postgres::types::ToSql;
use util::{
    anyhow::{anyhow, Result},
    chrono::{Duration, Utc},
    system_types::FullObjectIdOwned,
};

/// This module provides functionality for querying and manually updating vacuum/retention worker state,
/// primarily for integration testing purposes. It allows for checking the current status of vacuum
/// operations and manipulating worker state to test different scenarios.

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GlobalVacuumStatus {
    pub num_live_segments: i64,
    pub all_vacuumed_once: Option<bool>,
    pub all_vacuumed_up_to_date: Option<bool>,
    pub did_delete_files: Option<bool>,
}

pub async fn get_vacuum_status(
    pg_conn: &PostgresPool,
    object_ids: Option<Vec<FullObjectIdOwned>>,
    vacuum_index_stale_seconds: i64,
) -> Result<GlobalVacuumStatus> {
    let object_ids_filter = if object_ids.is_some() {
        "l.object_id = ANY($2::text[])".to_string()
    } else {
        "true".to_string()
    };

    let query = format!(
        r#"
        select
          count(*) as num_live_segments,
          bool_and(l.vacuum_index_last_successful_start_ts > '1970-01-01'::timestamptz) as all_vacuumed_once,
          bool_and(l.vacuum_index_last_successful_start_ts >= l.last_written_ts + make_interval(secs => $1::bigint)) as all_vacuumed_up_to_date,
          sum(coalesce((t.vacuum_index_info->>'num_deleted_files_batch')::int, 0)) > 0 as did_delete_files
        from {} l
        left join {} t on l.segment_id = t.segment_id
        where l.is_live and {}
        "#,
        SEGMENT_ID_TO_LIVENESS_TABLE, SEGMENT_ID_TO_TASK_INFO_TABLE, object_ids_filter,
    );

    let mut params: Vec<&(dyn ToSql + Sync)> = Vec::new();
    params.push(&vacuum_index_stale_seconds);
    let mut _object_ids_param = Vec::new();
    if let Some(object_ids) = object_ids {
        _object_ids_param = object_ids.iter().map(|id| id.to_string()).collect();
        params.push(&_object_ids_param);
    }

    let client = pg_conn.get_client().await?;
    let rows = client.query(&query, &params).await?;
    if rows.len() != 1 {
        return Err(anyhow!("Expected exactly one row"));
    }

    let row = &rows[0];
    let status = GlobalVacuumStatus {
        num_live_segments: row.get(0),
        all_vacuumed_once: row.get(1),
        all_vacuumed_up_to_date: row.get(2),
        did_delete_files: row.get(3),
    };
    Ok(status)
}

pub async fn reset_vacuum_state(
    pg_conn: &PostgresPool,
    object_ids: Option<Vec<FullObjectIdOwned>>,
) -> Result<u64> {
    let object_ids_filter = if object_ids.is_some() {
        "object_id = ANY($1::text[])".to_string()
    } else {
        "true".to_string()
    };

    let query = format!(
        r#"
        update {}
        set vacuum_index_last_successful_start_ts = '1970-01-01'::timestamptz
        where is_live and {}
        "#,
        SEGMENT_ID_TO_LIVENESS_TABLE, object_ids_filter
    );

    let mut params: Vec<&(dyn ToSql + Sync)> = Vec::new();
    let mut _object_ids_param = Vec::new();
    if let Some(object_ids) = object_ids {
        _object_ids_param = object_ids.iter().map(|id| id.to_string()).collect();
        params.push(&_object_ids_param);
    }

    let client = pg_conn.get_client().await?;
    let num_updated_segments = client.execute(&query, &params).await?;

    Ok(num_updated_segments)
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GlobalTimeBasedRetentionStatus {
    pub ran_once: bool,
    pub ran_recently: bool,
}

pub async fn get_time_based_retention_status(
    global_store: Arc<dyn GlobalStore>,
    recency_window_seconds: i64,
) -> Result<GlobalTimeBasedRetentionStatus> {
    let state = global_store.query_time_based_retention_state().await?;

    let ran_once = state.last_successful_start_ts.is_some();
    let ran_recently = match state.last_successful_start_ts {
        Some(last_successful_start_ts) => {
            last_successful_start_ts >= Utc::now() - Duration::seconds(recency_window_seconds)
        }
        None => false,
    };

    Ok(GlobalTimeBasedRetentionStatus {
        ran_once,
        ran_recently,
    })
}

pub async fn reset_time_based_retention_state(global_store: Arc<dyn GlobalStore>) -> Result<()> {
    global_store
        .upsert_time_based_retention_state(&TimeBasedRetentionState::default())
        .await
}
