use serde::{Deserialize, Serialize};
use util::{anyhow::Result, system_types::ObjectType};

#[derive(Debug, <PERSON>lone, Hash, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "snake_case")]
pub enum RetentionObjectType {
    ProjectLogs,
    // TODO: Enable retention for more object types
    // Experiment,
    // Dataset,
}

impl From<&RetentionObjectType> for ObjectType {
    fn from(retention_type: &RetentionObjectType) -> Self {
        match retention_type {
            RetentionObjectType::ProjectLogs => ObjectType::ProjectLogs,
        }
    }
}

impl TryFrom<ObjectType> for RetentionObjectType {
    type Error = util::anyhow::Error;

    fn try_from(object_type: ObjectType) -> Result<Self, Self::Error> {
        match object_type {
            ObjectType::ProjectLogs => Ok(RetentionObjectType::ProjectLogs),
            _ => Err(util::anyhow::anyhow!(
                "Object type {:?} is not supported for retention policies",
                object_type
            )),
        }
    }
}

impl std::fmt::Display for RetentionObjectType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        let s = match self {
            RetentionObjectType::ProjectLogs => "project_logs",
        };
        write!(f, "{}", s)
    }
}

impl RetentionObjectType {
    pub fn acl_object_type(&self) -> &'static str {
        match self {
            RetentionObjectType::ProjectLogs => "project_log",
        }
    }
}
