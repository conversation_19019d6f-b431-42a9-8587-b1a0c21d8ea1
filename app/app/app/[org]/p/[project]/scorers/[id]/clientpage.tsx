"use client";

import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";
import { useContext } from "react";
import { isEmpty } from "#/utils/object";
import { AccessFailed } from "#/ui/access-failed";
import { decodeURIComponentPatched } from "#/utils/url";
import { cn } from "#/utils/classnames";
import { HEIGHT_WITH_TOP_OFFSET } from "#/app/app/body-wrapper";
import { MainContentWrapper } from "#/ui/layout/main-content-wrapper";
import { useFunction } from "#/ui/prompts/use-function";
import { Loading } from "#/ui/loading";
import { FunctionDetail } from "#/ui/prompts/function-detail/function-detail";

export interface Params {
  org: string;
  project: string;
  id: string;
}

export default function ClientPage({ params }: { params: Params }) {
  const projectName = decodeURIComponentPatched(params.project);
  const { projectId } = useContext(ProjectContext);

  const { initialFunction, status, dml, auditLogScan, auditLogReady } =
    useFunction({
      objectType: "project_functions",
      functionId: params.id,
      promptVersion: null,
    });

  if (isEmpty(projectId)) {
    return <AccessFailed objectType="Project" objectName={projectName} />;
  }

  if (status === "loading") {
    return (
      <MainContentWrapper
        className={cn(
          "flex flex-col overflow-hidden py-0 flex-1 bg-primary-50",
          HEIGHT_WITH_TOP_OFFSET,
        )}
        hideFooter
      >
        <Loading />
      </MainContentWrapper>
    );
  }

  return (
    <FunctionDetail
      func={initialFunction}
      dml={dml}
      type="scorer"
      objectType="project_functions"
      projectId={projectId}
      auditLogScan={auditLogScan}
      auditLogReady={auditLogReady}
      initialTab="run"
      availableTabs={["run", "activity"]}
    />
  );
}
