import { TransactionIdField, useDuck<PERSON>onn, useDBQuery } from "#/utils/duckdb";
import { useOrg } from "#/utils/user";
import {
  type MouseEventHandler,
  type SetStateAction,
  type Dispatch,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { useAnalytics } from "#/ui/use-analytics";
import { TagBulkEditor, useHasTags } from "#/ui/trace/tags";
import { type ViewParams } from "#/utils/view/use-view";
import { Views } from "#/ui/views";
import {
  type Table as ArrowTable,
  Field,
  Schema,
  type TypeMap,
  Utf8,
} from "apache-arrow";
import { VizQuery } from "#/ui/viz-query";
import { type SavingState } from "#/ui/saving";
import { useTableSelection } from "#/ui/table/useTableSelection";
import { DuckDBTypeHints, parseObjectJSON } from "#/utils/schema";
import { buttonVariants } from "#/ui/button";
import { Bolt, MessageCircle, Percent, Plus, Route, Trash } from "lucide-react";
import { newId } from "#/utils/btapi/btapi";
import {
  createOrUpdatePrompt,
  deletePrompts,
  promptSchema,
  type UIFunction,
} from "./schema";
import { AccessFailed } from "#/ui/access-failed";
import { runAISearch } from "#/utils/ai-search/actions/events";
import { z } from "zod";
import {
  CancelSelectionButton,
  SelectionBarButton,
} from "#/ui/table/selection-bar";
import { ConfirmationDialog } from "#/ui/dialogs/confirmation";
import { pluralizeWithCount } from "#/utils/plurals";
import { BlueLink } from "#/ui/link";
import { TableEmptyState } from "#/ui/table/TableEmptyState";
import { type ClientOptions } from "openai";
import {
  type BTQLTableDefinition,
  useClauseChecker,
} from "#/utils/search-btql";
import {
  useActivePromptRowState,
  usePromptVersionState,
} from "#/ui/query-parameters";
import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";

import { toast } from "sonner";
import AppliedFilters from "#/ui/applied-filters";
import { FunctionDialog } from "./function-editor/function-dialog";
import { usePlaygroundCopilotContext } from "#/ui/copilot/playground";
import {
  type ViewType,
  type FunctionObjectType,
} from "@braintrust/core/typespecs";
import { capitalize } from "@braintrust/core";
import pluralize from "pluralize";
import { useFeatureFlags } from "#/lib/feature-flags";
import { zodToLogicalSchema } from "#/utils/zod-to-logical-schema";
import { useFunctionsListQuery } from "./use-functions-list";
import { slugify } from "#/utils/slug";
import { useAuth } from "@clerk/nextjs";
import { singleQuote } from "@braintrust/local/query";
import Link from "next/link";
import {
  getPlaygroundsLink,
  getSavedPromptLink,
} from "#/app/app/[org]/prompt/[prompt]/getPromptLink";
import { useRouter } from "next/navigation";
import { useFunction } from "./use-function";
import { useQueryClient } from "@tanstack/react-query";
import { cn } from "#/utils/classnames";

export const emptyFunctionMetaSchema = new Schema([
  Field.new({ name: "id", type: new Utf8() }),
  Field.new({ name: "user", type: new Utf8() }),
  Field.new({ name: "search_text", type: new Utf8() }),
]);

const functionsListMetadata: BTQLTableDefinition = {
  logical: zodToLogicalSchema(
    z.strictObject({
      id: z.string(),
      name: z.string(),
      slug: z.string(),
      description: z.string(),
      creator: z.record(z.any()),
      tags: z.array(z.string()),
    }),
  ),
  physical: {
    columns: {
      id: { path: ["id"], type: { type: "varchar" } },
      name: { path: ["name"], type: { type: "varchar" } },
      slug: { path: ["slug"], type: { type: "varchar" } },
      description: { path: ["description"], type: { type: "varchar" } },
      creator: { path: ["creator"], type: { type: "json" } },
      tags: { path: ["tags"], type: { type: "json" } },
    },
  },
};

export function FunctionsList({
  functionObjectType,
  setSavingState,
  scrollContainerRef,
  viewType,
  extraRightControls,
  textSearch,
}: {
  functionObjectType: FunctionObjectType;
  setSavingState?: Dispatch<SetStateAction<SavingState>>;
  scrollContainerRef: React.RefObject<HTMLDivElement | null>;
  viewType: ViewType;
  extraRightControls?: React.ReactNode;
  textSearch?: string;
}) {
  const { getToken } = useAuth();
  const org = useOrg();
  const router = useRouter();
  const { projectId, projectName } = useContext(ProjectContext);

  if (!projectId) {
    throw new Error("Cannot instantiate PromptsViewer outside project");
  }

  const {
    tableQuery,
    objectType,
    isUnsupported,
    viewProps,
    viewPropsWithSearch,
    projectedPaths,
    functionType,
  } = useFunctionsListQuery({
    functionObjectType,
    textSearch,
  });

  const { clauseChecker } = useClauseChecker(functionsListMetadata);
  const viewParams: ViewParams = {
    objectType: "project",
    objectId: projectId,
    viewType,
  };
  const pageIdentifier = functionObjectType + "-" + projectId;

  // Use internal search state for AppliedFilters
  const { search: internalSearch, setSearch } = viewProps;

  useAnalytics({
    page: {
      category: functionObjectType,
      props: {
        org_id: org.id,
        project_id: projectId,
      },
    },
  });

  const copilotContext = usePlaygroundCopilotContext();

  const [selectedRowId, setSelectedRowId] = useActivePromptRowState();
  const [promptVersion, setPromptVersion] = usePromptVersionState();

  const {
    dml,
    initialFunction,
    status,
    auditLogScan,
    auditLogReady,
    promptsSchema,
    promptsScan,
    promptsReady,
    error,
  } = useFunction({
    objectType,
    functionId: selectedRowId,
    setSavingState,
    promptVersion,
  });

  const { hasTags } = useHasTags({
    schema: promptsSchema,
  });

  // Check if there are any items of this function type in the database (before search/filters)
  const hasAnyItemsQuery = useMemo(() => {
    if (!promptsScan || !promptsSchema?.fields) return null;

    // Build the same function type filters as the main query
    const functionTypeFilters: string[] = ["TRUE"];
    if (promptsSchema?.fields.find((f) => f.name === "origin")) {
      functionTypeFilters.push(
        `e1.origin IS NULL or NOT COALESCE(json_extract_string(e1.origin, '$.internal')::boolean, false)`,
      );
    }

    if (promptsSchema?.fields.find((f) => f.name === "function_type")) {
      if (functionType) {
        functionTypeFilters.push(
          `e1.function_type = ${singleQuote(functionType)}`,
        );
      } else if (functionObjectType === "agent") {
        functionTypeFilters.push(
          `json_extract_string(e1.function_data, '$.type') = 'graph'`,
        );
      } else if (objectType === "project_prompts") {
        functionTypeFilters.push(
          `e1.function_type IS NULL AND json_extract_string(e1.prompt_data, '$.parser') IS NULL`,
        );
      }
    }

    return `SELECT 1 FROM (${promptsScan}) e1 WHERE ${functionTypeFilters.map((s) => `(${s})`).join(" AND ")} LIMIT 1`;
  }, [
    promptsScan,
    promptsSchema?.fields,
    functionType,
    functionObjectType,
    objectType,
  ]);

  const { data: anyItemsExist } = useDBQuery(hasAnyItemsQuery, [promptsReady]);

  const hasAnyPrompts = useMemo(() => {
    return anyItemsExist && anyItemsExist.numRows > 0;
  }, [anyItemsExist]);

  const {
    selectedRows: rowSelection,
    setSelectedRows: setRowSelection,
    getSelectedRowsWithData,
    selectedRowsNumber,
    deselectAllTableRows,
    getRow,
    tableRef,
  } = useTableSelection();

  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  const [rowsToDelete, setRowsToDelete] = useState<any[]>([]);

  const runPromptsAISearch = (openAIOpts: ClientOptions, query: string) =>
    runAISearch({
      openAIOpts,
      apiUrl: org.api_url,
      query,
      orgName: org.name,
      searchType: "prompts",
      aiSchemaColumns: projectedPaths,
    });

  const extraLeftControls = (
    <>
      {["prompt", "scorer"].includes(functionObjectType) && (
        <Link
          className={cn(buttonVariants({ variant: "primary", size: "xs" }))}
          href={getSavedPromptLink({
            orgName: org.name,
            projectSlug: projectName,
            promptId: "new",
            type: functionObjectType,
          })}
        >
          <Plus className="size-3" />
          {capitalize(functionObjectType)}
        </Link>
      )}
      <Views
        pageIdentifier={pageIdentifier}
        viewParams={viewParams}
        viewProps={viewProps}
      />
    </>
  );

  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  const rowEvents = useMemo<Record<string, (row: any) => MouseEventHandler>>(
    () => ({
      onClick: (row) => () => {
        if (
          functionObjectType === "prompt" ||
          functionObjectType === "scorer"
        ) {
          router.push(
            getSavedPromptLink({
              orgName: org.name,
              projectSlug: projectName,
              promptId: row.original.id,
              type: functionObjectType,
            }),
          );
        } else {
          setSelectedRowId(row.original.id);
        }
      },
    }),
    [functionObjectType, router, projectName, org.name, setSelectedRowId],
  );

  const vizQueryRef = useRef<{
    data: ArrowTable<TypeMap> | null;
  }>(null);

  const initializedCopilotPrompts = useRef(false);
  const {
    flags: { functionTools },
  } = useFeatureFlags();

  const { conn } = useDuckConn();
  useEffect(() => {
    if (initializedCopilotPrompts.current) {
      return;
    }

    (async () => {
      if (!conn || !promptsScan) {
        return;
      }

      // Set this up front to avoid race conditions
      initializedCopilotPrompts.current = true;
      const promptData = await conn.query(
        `SELECT * FROM (${promptsScan}) ORDER BY ${TransactionIdField} DESC`,
      );
      if (!promptData) {
        initializedCopilotPrompts.current = false;
        return;
      }
      const promptDbData = promptData
        .toArray()
        .map((r) => parseObjectJSON(objectType, r.toJSON()));
      const parsed = z.array(promptSchema).safeParse(promptDbData);
      if (!parsed.success) {
        console.error(parsed.error);
        return;
      }
      const prompts = parsed.data;
      for (const prompt of prompts.reverse()) {
        const full = copilotContext.visitPrompt(prompt);
        if (full) {
          break;
        }
      }
    })();
  }, [conn, copilotContext, objectType, promptsScan]);

  const neverVisibleColumns = useMemo(() => {
    return new Set([TransactionIdField]);
  }, []);
  const queryClient = useQueryClient();

  if (!org.name) {
    return <AccessFailed objectType={"Prompts"} objectName={""} />;
  } else if (isUnsupported) {
    return (
      <span>
        Update your stack to get access to{" "}
        <BlueLink href="/docs/guides/prompts">saved prompts</BlueLink>.
      </span>
    );
  }

  return (
    <>
      <VizQuery
        {...tableQuery}
        className="pt-3"
        viewProps={viewPropsWithSearch}
        vizQueryRef={vizQueryRef}
        rowSelection={rowSelection}
        setRowSelection={setRowSelection}
        tableRef={tableRef}
        afterToolbarSlot={
          <div className="sticky left-0 flex w-full flex-wrap gap-1 empty:hidden">
            <AppliedFilters
              search={internalSearch}
              className="pt-2"
              clauseChecker={clauseChecker}
              setSearch={setSearch}
              runAISearch={runPromptsAISearch}
              fromClause={`project_functions(${singleQuote(projectId)})`}
            />
          </div>
        }
        scrollContainerRef={scrollContainerRef}
        typeHints={DuckDBTypeHints.dataset}
        extraLeftControls={extraLeftControls}
        extraRightControls={extraRightControls}
        initiallyVisibleColumns={{ id: false }}
        neverVisibleColumns={neverVisibleColumns}
        rowEvents={rowEvents}
        error={error && `${error}`}
        runAISearch={runPromptsAISearch}
        toolbarSlot={
          selectedRowsNumber > 0 ? (
            <>
              <CancelSelectionButton
                onCancelSelection={deselectAllTableRows}
                selectedRowsNumber={selectedRowsNumber}
              />
              {hasTags && dml && (
                <TagBulkEditor
                  dml={dml}
                  selectionProps={{
                    selectedRows: rowSelection,
                    getSelectedRowsWithData,
                  }}
                />
              )}
              <SelectionBarButton
                title="Delete selected prompts"
                onClick={() => {
                  setRowsToDelete(getSelectedRowsWithData());
                }}
                Icon={Trash}
              />
            </>
          ) : undefined
        }
        hasNoRowsComponent={
          // Only show empty state if there are truly no prompts in the database
          // This prevents showing it during search/filter transitions
          !hasAnyPrompts ? (
            <TableEmptyState
              className="mt-3"
              Icon={
                functionObjectType === "prompt"
                  ? MessageCircle
                  : functionObjectType === "tool"
                    ? Bolt
                    : functionObjectType === "agent"
                      ? Route
                      : Percent
              }
              label={`No ${pluralize(functionObjectType)} in this project yet`}
            >
              {functionObjectType === "tool" ? (
                <span className="text-sm text-primary-500">
                  {!functionTools ? (
                    "Update your stack to get access to tools."
                  ) : (
                    <>
                      Currently, tools can only be created via code. See the{" "}
                      <BlueLink
                        href="/docs/guides/functions/tools"
                        target="_blank"
                      >
                        docs
                      </BlueLink>{" "}
                      for details.
                    </>
                  )}
                </span>
              ) : functionObjectType === "agent" ? (
                <span className="text-sm text-primary-500">
                  Currently, agents can only be created from a{" "}
                  <Link
                    className="text-accent-600"
                    href={getPlaygroundsLink({
                      projectName,
                      orgName: org.name,
                    })}
                  >
                    playground
                  </Link>
                  .
                </span>
              ) : (
                <Link
                  className={cn(buttonVariants({ variant: "border" }))}
                  href={getSavedPromptLink({
                    orgName: org.name,
                    projectSlug: projectName,
                    promptId: "new",
                    type: functionObjectType,
                  })}
                >
                  Create {functionObjectType}
                </Link>
              )}
            </TableEmptyState>
          ) : null
        }
      />
      <FunctionDialog
        initialFunction={initialFunction}
        status={status}
        context="functions"
        // IMPORTANT: prompt version must be included so that prompt state is reset when the version changes
        identifier={`${functionObjectType}-${initialFunction?.id}-${initialFunction?.[TransactionIdField]}`}
        type={functionObjectType}
        opened={!!selectedRowId && rowsToDelete.length === 0}
        setOpened={(o) => {
          if (!o) {
            setSelectedRowId(null);
            setPromptVersion(null);
          }
        }}
        mode={{
          type: selectedRowId === "new" ? "create" : "update",
          onDelete: () => {
            if (!selectedRowId) return;
            setRowsToDelete([getRow(selectedRowId)]);
          },
          onDuplicate: async (prompt: UIFunction) => {
            if (!org.id) {
              toast.error("Unauthorized");
              return null;
            }
            if (!projectId) {
              toast.error("No project selected");
              return null;
            }

            const newPromptId = newId();

            deselectAllTableRows();
            try {
              const name = `${prompt.name} (copy)`;
              const slug = slugify(name);
              await createOrUpdatePrompt({
                dml,
                orgId: org.id,
                update: false,
                updateSlug: false,
                prompt: {
                  function_data: prompt.function_data,
                  description: prompt.description,
                  function_type: prompt.function_type,
                  id: newPromptId,
                  name,
                  slug,
                  _xact_id: "0",
                  project_id: projectId,
                  tags: prompt.tags,
                  prompt_data: prompt.prompt_data,
                },
              });

              // Do this after the prompt is created, so that we have a new db record to select.
              setSelectedRowId(newPromptId);
              setPromptVersion(null);
              toast.success(`Created ${name}`);
            } catch (e) {
              toast.error(`Failed to duplicate prompt`, {
                description: `${e}`,
              });
            }
          },
          upsert: async (prompt, updateSlug) => {
            if (!org.id) {
              toast.error("Unauthorized");
              return null;
            }
            if (!projectId) {
              toast.error("No project selected");
              return null;
            }

            const xactId = await createOrUpdatePrompt({
              dml,
              orgId: org.id,
              update: selectedRowId !== "new",
              updateSlug,
              prompt,
            });

            // Do this after the prompt is created, so that we have a new db record to select.
            // The prompt content will already have been updated by the user, so there's no
            // real benefit to doing this in an optimistic update and it adds jank.
            setSelectedRowId(prompt.id);
            setPromptVersion(null);

            return xactId;
          },
        }}
        activityProps={
          selectedRowId === "new"
            ? undefined
            : {
                commentOn: dml.commentOn,
                deleteComment: dml.deleteComment,
                auditLogScan,
                auditLogReady,
                rowId: selectedRowId!,
                setPromptVersion,
                selectedVersion: initialFunction?.[TransactionIdField] ?? null,
              }
        }
        objectType={objectType}
        orgName={org.name}
        projectName={projectName}
        projectId={projectId}
        copilotContext={copilotContext}
      />
      {rowsToDelete.length > 0 && (
        <ConfirmationDialog
          open={rowsToDelete.length > 0}
          onOpenChange={() => setRowsToDelete([])}
          title={`Delete ${pluralizeWithCount(
            rowsToDelete.length,
            functionObjectType,
          )}`}
          description={`Are you sure you want to delete ${pluralizeWithCount(
            rowsToDelete.length,
            functionObjectType,
          )}?`}
          confirmText="Delete"
          onConfirm={async () => {
            if (!org.id || !projectId) {
              toast.error("Cannot delete without an org or project");
              return;
            }
            deselectAllTableRows();
            setSelectedRowId(null);
            setPromptVersion(null);

            try {
              await deletePrompts({
                dml,
                orgId: org.id,
                projectId,
                rowsToDelete,
                getToken,
              });
              queryClient.invalidateQueries({
                queryKey: ["fetchPromptListing", { orgName: org.name }],
              });
            } catch (e) {
              toast.error(`Failed to delete prompts`, {
                description: `${e}`,
              });
            }
          }}
        />
      )}
    </>
  );
}
