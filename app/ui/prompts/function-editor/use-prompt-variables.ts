import { getMustacheVars } from "#/app/app/[org]/prompt/[prompt]/helpers";
import { isEmpty, updatePathMut } from "#/utils/object";
import { useDebounce } from "#/utils/useDebouncedCallback";
import { getObjValueByPath } from "@braintrust/core";
import { produce } from "immer";
import {
  type Dispatch,
  type RefObject,
  type SetStateAction,
  useCallback,
  useMemo,
} from "react";
import { type PromptBlockData } from "#/ui/prompts/schema";
import { isDatasetReservedName } from "#/ui/prompts/hooks";

const PROMPT_VARIABLES_DEBOUNCE_DELAY = 1000;

export function usePromptVariables({
  promptContent,
  functionSchema,
  runData,
  setRunData,
  touchedEditorRef,
  shouldPrependInput = false,
}: {
  promptContent?: PromptBlockData | null;
  functionSchema?: string[];
  runData: Record<string, unknown>;
  setRunData: Dispatch<SetStateAction<Record<string, unknown>>>;
  touchedEditorRef?: RefObject<boolean>;
  shouldPrependInput?: boolean;
}) {
  const promptVariables = useDebounce(
    useMemo(() => {
      if (!promptContent) {
        return undefined;
      }
      const variables = (
        promptContent?.type === "completion"
          ? getMustacheVars(promptContent.content)
          : promptContent?.type === "chat"
            ? promptContent.messages
                .map((m) =>
                  m.content
                    ? typeof m.content === "string"
                      ? m.content
                      : JSON.stringify(m.content)
                    : "",
                )
                .flatMap(getMustacheVars)
            : []
      )
        .map((v) => v[1])
        .filter((v) => v !== "");

      return Array.from(new Set(variables));
    }, [promptContent]),
    PROMPT_VARIABLES_DEBOUNCE_DELAY,
  );

  const promptVariableUpdates = useMemo(() => {
    if (!isEmpty(promptVariables)) {
      return promptVariables.map((v) => {
        if (!isDatasetReservedName(v) && shouldPrependInput) {
          return `input.${v}`;
        }
        return v;
      });
    }
    if (!isEmpty(functionSchema)) {
      return functionSchema;
    }
    return null;
  }, [promptVariables, functionSchema, shouldPrependInput]);

  const isMissingPromptVariables = useMemo(() => {
    if (!promptVariableUpdates || promptVariableUpdates.length === 0) {
      return false;
    }

    for (const v of promptVariableUpdates) {
      const path = v.split(".");
      const value = getObjValueByPath(runData, path);
      if (value == null) {
        return true;
      }
    }

    return false;
  }, [promptVariableUpdates, runData]);

  const addPromptVariables = useCallback(() => {
    setRunData(
      produce((currentValue) => {
        // If the user has touched the input editor and set non-empty values, don't auto-remove any variables, but auto-add any new ones.
        // If they haven't, auto-set the variables to whatever has been parsed from the prompt.
        const shouldAppend =
          touchedEditorRef?.current && hasNonEmptyValues(currentValue);
        const baseValue = shouldAppend ? currentValue : {};
        for (const v of promptVariableUpdates ?? []) {
          updatePathMut(baseValue, v.split("."), "", {
            keepExistingValue: shouldAppend,
          });
        }

        return baseValue;
      }),
    );
  }, [promptVariableUpdates, setRunData, touchedEditorRef]);

  return {
    isMissingPromptVariables,
    addPromptVariables,
  };
}

const hasNonEmptyValues = (obj: Record<string, unknown> | unknown): boolean => {
  if (obj === null || obj === undefined || obj === "") {
    return false;
  }
  if (typeof obj === "object" && !Array.isArray(obj) && obj !== null) {
    return Object.values(obj).some(hasNonEmptyValues);
  }
  return true;
};
