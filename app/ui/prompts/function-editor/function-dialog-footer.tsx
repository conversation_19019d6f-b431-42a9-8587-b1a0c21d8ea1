import { CopyAIProxyCode } from "#/app/app/[org]/p/[project]/prompts/copy-ai-proxy-code";
import { But<PERSON>, buttonVariants } from "#/ui/button";
import { DialogFooter } from "#/ui/dialog";
import { type FunctionObjectType } from "@braintrust/core/typespecs";
import { AlertTriangle, Code, Copy, Trash2 } from "lucide-react";
import {
  type Dispatch,
  type RefObject,
  type SetStateAction,
  useMemo,
  useState,
} from "react";
import {
  type SyncedPlaygroundBlock,
  type UIFunction,
} from "#/ui/prompts/schema";
import { useSyncedPrompts } from "#/app/app/[org]/p/[project]/prompts/synced/use-synced-prompts";
import { atom, useAtomValue } from "jotai";
import { TransactionIdField } from "@braintrust/local/query";
import { cn } from "#/utils/classnames";
import Link from "next/link";
import { getSavedPromptLink } from "#/app/app/[org]/prompt/[prompt]/getPromptLink";
import { type FunctionEditorContext, type Mode } from "./types";
import { CreatePlaygroundButton } from "./create-playground";
import { isTooComplex } from "./function-editor";
import { FunctionEditorSubmitButton } from "./function-editor-submit-button";
import { isEmpty, isObject } from "@braintrust/core";

export function FunctionDialogFooter({
  mode,
  type,
  getOutputPrompt,
  isSlugTouchedRef,
  isDirtyRef,
  error,
  setError,
  hasLintErrors,
  initialDirtyFunctionComparisonBase,
  orgName,
  projectId,
  projectName,
  promptName,
  dataEditorValue,
  context,
}: {
  mode: Mode;
  type: FunctionObjectType;
  getOutputPrompt: (promptState: SyncedPlaygroundBlock) => UIFunction;
  isSlugTouchedRef: RefObject<boolean>;
  isDirtyRef: RefObject<boolean>;
  error: string | null;
  setError: Dispatch<SetStateAction<string | null>>;
  hasLintErrors: boolean;
  initialDirtyFunctionComparisonBase: UIFunction | null;
  orgName: string;
  projectId: string;
  projectName: string;
  promptName?: string;
  dataEditorValue: Record<string, unknown>;
  context: FunctionEditorContext;
}) {
  const { sortedSyncedPromptsAtom_ROOT } = useSyncedPrompts();
  const prompt = useAtomValue(
    useMemo(
      () => atom((get) => get(sortedSyncedPromptsAtom_ROOT)[0]),
      [sortedSyncedPromptsAtom_ROOT],
    ),
  );
  const [updating, setUpdating] = useState(false);

  return (
    <DialogFooter className="flex-none items-center border-t px-4 py-3">
      <div className="flex flex-1 items-center gap-2">
        {type === "prompt" && prompt.prompt_data && (
          <CopyAIProxyCode promptData={prompt.prompt_data}>
            <Button size="xs" variant="ghost" Icon={Code}>
              Code snippet
            </Button>
          </CopyAIProxyCode>
        )}
        {mode.type === "update" && !!mode.onDuplicate && (
          <Button
            size="xs"
            variant="ghost"
            onClick={(e) => {
              e.preventDefault();
              const outputPrompt = getOutputPrompt(prompt);
              mode.onDuplicate?.(outputPrompt);
            }}
            Icon={Copy}
          >
            Duplicate
          </Button>
        )}
        {mode.type === "update" && !!mode.onDelete && (
          <Button
            Icon={Trash2}
            size="xs"
            variant="ghost"
            onClick={(e) => {
              e.preventDefault();
              mode.onDelete?.();
            }}
            disabled={updating}
          >
            Delete
          </Button>
        )}
        {mode.type !== "create" && context !== "playground" && (
          <CreatePlaygroundButton
            orgName={orgName}
            projectName={projectName}
            type={type}
            datasetRows={
              !isEmpty(dataEditorValue) &&
              isObject(dataEditorValue) &&
              Object.keys(dataEditorValue).length > 0
                ? [
                    {
                      input: dataEditorValue,
                    },
                  ]
                : undefined
            }
            prompt={prompt}
            promptName={promptName}
            projectId={projectId}
            mode={mode}
            getOutputPrompt={getOutputPrompt}
          />
        )}
      </div>
      {error && (
        <div className="flex items-center gap-1.5 px-2 text-xs font-medium text-bad-700">
          <AlertTriangle className="size-3 flex-none" />
          {error}
        </div>
      )}
      {
        // TODO: support linking to scorers in other projects
        mode.type === "view_saved" &&
          projectId === initialDirtyFunctionComparisonBase?.project_id && (
            <Link
              href={getSavedPromptLink({
                orgName,
                projectSlug: projectName,
                promptId: prompt.id,
                promptVersion: prompt[TransactionIdField],
                type,
              })}
              className={cn(
                buttonVariants({
                  size: "xs",
                }),
              )}
            >
              Edit in library
            </Link>
          )
      }
      {mode.type !== "view_saved" && !isTooComplex(prompt) && (
        <FunctionEditorSubmitButton
          mode={mode}
          type={type}
          getOutputPrompt={getOutputPrompt}
          prompt={prompt}
          isSlugTouchedRef={isSlugTouchedRef}
          setError={setError}
          upsert={mode.upsert}
          hasLintErrors={hasLintErrors}
          initialDirtyFunctionComparisonBase={
            initialDirtyFunctionComparisonBase
          }
          updating={updating}
          setUpdating={setUpdating}
          isDirtyRef={isDirtyRef}
          orgName={orgName}
          projectName={projectName}
        />
      )}
    </DialogFooter>
  );
}
