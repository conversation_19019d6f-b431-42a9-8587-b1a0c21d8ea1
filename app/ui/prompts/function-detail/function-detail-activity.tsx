import { type FunctionObjectType } from "@braintrust/core/typespecs";
import { type ObjectType } from "#/ui/prompts/function-editor/types";
import { useRowAuditLog } from "#/ui/trace/query";
import { Activity } from "#/ui/trace/activity";
import { type CommentData } from "@braintrust/local/api-schema";
import { newId } from "braintrust";
import { useCallback, useEffect, useMemo, useRef } from "react";
import {
  type SyncedPlaygroundBlock,
  type UIFunction,
} from "#/ui/prompts/schema";
import { type CommentFn, type DeleteCommentFn } from "#/utils/mutable-object";
import { TransactionIdField } from "@braintrust/local/query";
import { isEmpty, prettifyXact } from "@braintrust/core";
import { useOpenedPlaygroundPrompt } from "#/app/app/[org]/p/[project]/prompts/open";
import { DataTextEditor } from "#/ui/data-text-editor";
import { excludeKeys } from "#/app/app/[org]/p/[project]/prompts/utils";
import { History } from "lucide-react";
import { Button } from "#/ui/button";
import { BasicTooltip } from "#/ui/tooltip";
import { useSyncedPrompts } from "#/app/app/[org]/p/[project]/prompts/synced/use-synced-prompts";
import { toast } from "sonner";
import { atom, useAtomValue } from "jotai";
import isEqual from "lodash.isequal";
import { usePromptVersionState } from "#/ui/query-parameters";
import { LibraryItemLinks } from "#/app/app/[org]/p/[project]/library/library-item-links";

const EXCLUDED_KEYS = [
  "id",
  "project_id",
  "org_id",
  "_xact_id",
  "function_schema",
  "origin",
  "log_id",
];
const ALLOWED_RENDER_OPTIONS = ["json" as const, "yaml" as const];

export function FunctionDetailActivity({
  commentOn,
  deleteComment,
  auditLogReady,
  auditLogScan,
  objectType,
  func,
  getOutputPrompt,
  type,
  isActive,
  modeType,
  projectName,
}: {
  isActive: boolean;
  auditLogReady: number[];
  auditLogScan: string | null;
  commentOn: CommentFn;
  deleteComment: DeleteCommentFn;
  func: UIFunction;
  getOutputPrompt: (promptState: SyncedPlaygroundBlock) => UIFunction;
  objectType: ObjectType;
  type: FunctionObjectType;
  modeType: "create" | "update";
  projectName: string;
}) {
  const { auditLogData } = useRowAuditLog({
    auditLogScan,
    auditLogReady,
    rowId: func.id,
    dynamicObjectId: null,
    objectType,
  });

  const [_selectedVersion, setSelectedVersion] = usePromptVersionState();
  const selectedVersion = _selectedVersion ?? func[TransactionIdField];

  const { prompt: versionedPrompt, status } = useOpenedPlaygroundPrompt({
    promptId: func.id,
    projectId: func.project_id,
    promptVersion: selectedVersion ?? undefined,
  });
  // Save the previous versioned prompt to avoid flickering
  const prevVersionedPrompt = useRef(versionedPrompt);
  useEffect(() => {
    // When saving a new prompt, the latest versioned prompt will be transiently empty
    if (isEmpty(versionedPrompt) || Object.keys(versionedPrompt).length === 0) {
      return;
    }
    prevVersionedPrompt.current = versionedPrompt;
  }, [versionedPrompt]);

  const { resetPrompt, resetFunction_ROOT, sortedSyncedPromptsAtom_ROOT } =
    useSyncedPrompts();
  const livePrompt = useAtomValue(
    useMemo(
      () => atom((get) => get(sortedSyncedPromptsAtom_ROOT)[0]),
      [sortedSyncedPromptsAtom_ROOT],
    ),
  );

  const onResetPrompt = () => {
    if (
      status !== "loaded" ||
      (!versionedPrompt?.prompt_data && !versionedPrompt?.function_data)
    ) {
      toast.error(`Could not reset ${type} (still initializing)`);
      return;
    }
    return type === "prompt"
      ? resetPrompt({
          id: func.id,
          prompt: versionedPrompt,
          includeOrigin: false,
        })
      : resetFunction_ROOT({
          id: func.id,
          prompt: versionedPrompt,
          includeOrigin: false,
        });
  };

  const addComment = useCallback(
    async (comment: CommentData) => {
      const commentId = newId();
      const transactionId = await commentOn([
        {
          id: commentId,
          row: func,
          comment,
        },
      ]);
      return { transactionId, commentId };
    },
    [commentOn, func],
  );

  const diff = useMemo(() => {
    // Don't compute diff while editing prompt if diff isn't needed
    if (!isActive) {
      return {
        current: null,
        versioned: null,
        isEqual: true,
      };
    }

    const current = excludeKeys(getOutputPrompt(livePrompt), EXCLUDED_KEYS);

    const versionedPromptToUse =
      // eslint-disable-next-line react-compiler/react-compiler
      status === "loading" ? prevVersionedPrompt.current : versionedPrompt;
    const versionedPromptExcludedKeys =
      versionedPromptToUse?.function_data.type === "code"
        ? [...EXCLUDED_KEYS, "prompt_data"]
        : EXCLUDED_KEYS;
    const versioned = excludeKeys(
      versionedPromptToUse,
      versionedPromptExcludedKeys,
    );
    return {
      current,
      versioned,
      isEqual: isEqual(current, versioned),
    };
  }, [getOutputPrompt, livePrompt, status, versionedPrompt, isActive]);

  return (
    <div className="flex flex-1">
      <div className="flex-1 overflow-y-scroll border-r px-3 py-4 border-primary-200/80">
        <div className="text-pretty pb-2 text-xs text-primary-400">
          This is an interactive diff of the current {type} editor data compared
          to <code>{prettifyXact(selectedVersion ?? "")}</code>
          {selectedVersion === func[TransactionIdField] ? " (latest)" : ""}.
        </div>
        <DataTextEditor
          allowedRenderOptions={ALLOWED_RENDER_OPTIONS}
          diffValue={diff.versioned}
          readOnly
          value={diff.current}
          extraActions={
            !diff.isEqual && (
              <BasicTooltip tooltipContent={`Edit ${type} from this version`}>
                <Button
                  variant="ghost"
                  size="xs"
                  className="text-primary-400"
                  Icon={History}
                  onClick={onResetPrompt}
                />
              </BasicTooltip>
            )
          }
        />
      </div>
      <div className="flex flex-1 flex-col gap-3 overflow-y-scroll px-3 py-4">
        {modeType !== "create" && (
          <>
            <div>
              <LibraryItemLinks
                projectName={projectName}
                objectType={type}
                objectId={func.id}
                objectName={func.name ?? undefined}
                objectSlug={func.slug ?? undefined}
              />
            </div>
          </>
        )}
        <Activity
          auditLog={auditLogData}
          addComment={addComment}
          deleteComment={deleteComment}
          objectName={type}
          onSelectVersion={setSelectedVersion}
          selectedVersion={selectedVersion}
          className="mx-0"
          entityName={func.name}
          functionType={type}
          objectId={func.id}
        />
      </div>
    </div>
  );
}
