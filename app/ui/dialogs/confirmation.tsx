import {
  Dialog,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "#/ui/dialog";
import React from "react";
import { But<PERSON> } from "#/ui/button";

export const ConfirmationDialog = ({
  open,
  onOpenChange,
  title,
  description,
  confirmText,
  confirmSecondaryText,
  onConfirm,
  onConfirmSecondary,
  onCancel,
  children,
}: React.PropsWithChildren<{
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title: React.ReactNode;
  description?: React.ReactNode;
  confirmText: string;
  confirmSecondaryText?: string;
  onConfirm: () => void;
  onConfirmSecondary?: () => void;
  onCancel?: () => void;
}>) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[540px]">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          {description && <DialogDescription>{description}</DialogDescription>}
        </DialogHeader>
        {children}
        <DialogFooter>
          <Button
            size="sm"
            variant="ghost"
            onClick={() => {
              onCancel?.();
              onOpenChange(false);
            }}
          >
            Cancel
          </Button>
          {confirmSecondaryText && (
            <Button
              size="sm"
              variant="ghost"
              onClick={() => {
                onConfirmSecondary?.();
                onOpenChange(false);
              }}
            >
              {confirmSecondaryText}
            </Button>
          )}
          <Button
            size="sm"
            variant="primary"
            onClick={() => {
              onConfirm();
              onOpenChange(false);
            }}
          >
            {confirmText}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
