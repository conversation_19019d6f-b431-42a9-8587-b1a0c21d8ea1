import { chatCompletionMessageParamSchema } from "@braintrust/core/typespecs";
import { anthropicMessageParamSchema } from "@braintrust/proxy/types";
import { z } from "zod";
import { CollapsibleSection } from "./collapsible-section";
import { <PERSON>Viewer } from "./tree";
import { safeParseImage } from "./image";
import { MarkdownViewer } from "./markdown";
import { CopyToClipboardButton } from "./copy-to-clipboard-button";
import { deserializePlainStringAsJSON } from "braintrust";
import { Bolt, BrainIcon, Plus, Trash } from "lucide-react";
import { cn } from "#/utils/classnames";
import { SyntaxHighlight } from "./syntax-highlighter";
import { ToolsViewer } from "./tool-viewer";
import { motion } from "motion/react";
import { Button } from "./button";
import { BasicTooltip } from "./tooltip";

const messagesSchema = z.array(chatCompletionMessageParamSchema);
const anthropicMessagesSchema = z.array(anthropicMessageParamSchema);

export const parseLLMSpanPart = (
  value: unknown,
):
  | null
  | z.infer<typeof messagesSchema>
  | z.infer<typeof anthropicMessagesSchema> => {
  const parsed = messagesSchema.safeParse(value);
  if (parsed.success) {
    return parsed.data;
  }

  const anthropicParsed = anthropicMessagesSchema.safeParse(value);
  if (anthropicParsed.success) {
    return anthropicParsed.data;
  }

  const compositeMessage = z.array(
    z.object({ message: chatCompletionMessageParamSchema }),
  );
  const parsedOutputMessage = compositeMessage.safeParse(value);
  if (parsedOutputMessage.success) {
    return parsedOutputMessage.data.map((x) => x.message);
  }

  const compositeAnthropicMessage = z.array(
    z.object({ message: anthropicMessageParamSchema }),
  );
  const parsedOutputAnthropicMessage =
    compositeAnthropicMessage.safeParse(value);
  if (parsedOutputAnthropicMessage.success) {
    return parsedOutputAnthropicMessage.data.map((x) => x.message);
  }

  return null;
};

function WithCopyButton({
  children,
  value,
  showButton = true,
}: {
  children: React.ReactNode;
  value: string;
  showButton?: boolean;
}) {
  return showButton ? (
    <div className="flex w-full">
      {children}
      <div className="ml-auto flex w-fit flex-none gap-1 opacity-0 transition-opacity group-hover:opacity-100">
        <CopyToClipboardButton
          textToCopy={value}
          size="icon"
          className="size-6 text-primary-500"
          variant="ghost"
          copiedMessage="Message copied to clipboard"
        />
      </div>
    </div>
  ) : (
    children
  );
}

function LLMTextMessage({
  value,
  isRaw,
  markdownClassName,
  hideCopyButton,
}: {
  value: string;
  isRaw?: boolean;
  markdownClassName?: string;
  hideCopyButton?: boolean;
}) {
  const { error: isNotJSON } = deserializePlainStringAsJSON(value);

  return (
    <WithCopyButton value={value} showButton={!!isNotJSON && !hideCopyButton}>
      {isRaw ? (
        <p className="grow whitespace-pre-wrap text-sm leading-6 empty:hidden">
          {value}
        </p>
      ) : (
        <MarkdownViewer
          value={value}
          className={cn(
            "grow whitespace-normal empty:hidden text-sm leading-6 prose-p:whitespace-pre-line",
            markdownClassName,
          )}
        />
      )}
    </WithCopyButton>
  );
}

function getLabel(type: string): string {
  switch (type) {
    case "tool_use":
      return "Tool use";
    case "tool_result":
      return "Tool result";
    case "image_url":
    case "image":
      return "Image";
    default:
      return "Text";
  }
}

function MessageContent({
  messageContent,
  isRaw,
  markdownClassName,
  hideCopyButton,
}: {
  messageContent:
    | z.infer<typeof messagesSchema>[number]["content"]
    | z.infer<typeof anthropicMessagesSchema>[number]["content"];
  isRaw?: boolean;
  markdownClassName?: string;
  hideCopyButton?: boolean;
}) {
  if (typeof messageContent === "string") {
    return (
      <LLMTextMessage
        value={messageContent}
        isRaw={isRaw}
        markdownClassName={markdownClassName}
        hideCopyButton={hideCopyButton}
      />
    );
  }

  const singleTextMessage =
    messageContent?.length === 1 && messageContent[0].type === "text";
  if (isRaw && !singleTextMessage) {
    const jsonString = JSON.stringify(messageContent, null, 2);
    return (
      <WithCopyButton value={jsonString}>
        <SyntaxHighlight
          content={jsonString}
          language="json"
          className="bg-transparent"
        />
      </WithCopyButton>
    );
  }

  return messageContent?.map((part, i) => (
    <div key={i}>
      {messageContent.length > 1 && (
        <div className="my-1 text-xs text-primary-600">
          {getLabel(part.type)}
        </div>
      )}
      {part.type === "text" ? (
        <LLMTextMessage
          value={part.text}
          isRaw={isRaw}
          markdownClassName={markdownClassName}
          hideCopyButton={hideCopyButton}
        />
      ) : part.type === "image_url" || part.type === "image" ? (
        safeParseImage({ data: part, key: i })
      ) : (
        <WithCopyButton value={JSON.stringify(part)}>
          <TreeViewer value={part} />
        </WithCopyButton>
      )}
    </div>
  ));
}

export type LLMMessageType =
  | z.infer<typeof messagesSchema>[number]
  | z.infer<typeof anthropicMessagesSchema>[number];

export const LLMMessage = ({
  message,
  isRaw,
  markdownClassName,
  hideCopyButton,
}: {
  message: LLMMessageType;
  isRaw?: boolean;
  markdownClassName?: string;
  hideCopyButton?: boolean;
}) => {
  return (
    <div className="flex flex-col">
      {message.role === "assistant" &&
        "reasoning" in message &&
        message.reasoning && (
          <>
            <CollapsibleSection
              defaultCollapsed
              className="mb-0 mt-2 inline-flex h-7 flex-none text-primary-600"
              title={
                <>
                  <BrainIcon className="mr-1 size-3" /> Reasoning
                </>
              }
            >
              <div className="text-primary-500">
                {message.reasoning.map((r, idx) => (
                  <LLMTextMessage
                    key={idx}
                    value={r.content ?? ""}
                    isRaw={isRaw}
                    markdownClassName={cn(
                      "text-xs font-normal leading-4",
                      markdownClassName,
                    )}
                    hideCopyButton={hideCopyButton}
                  />
                ))}
              </div>
            </CollapsibleSection>
          </>
        )}

      <MessageContent
        messageContent={message.content}
        isRaw={isRaw}
        markdownClassName={markdownClassName}
        hideCopyButton={hideCopyButton}
      />
      {/* OpenAI formatted function call */}
      {message.role === "assistant" &&
        "function_call" in message &&
        message.function_call && (
          <div className="mb-2">
            <div className="my-1 text-xs text-primary-600">Function call</div>
            {isRaw ? (
              <SyntaxHighlight
                content={JSON.stringify(message.function_call, null, 2)}
                language="json"
                className="bg-transparent"
              />
            ) : (
              <TreeViewer value={message.function_call} />
            )}
          </div>
        )}

      {/* OpenAI formatted tool calls */}
      {message.role === "assistant" &&
        "tool_calls" in message &&
        message.tool_calls && (
          <div className="mb-2 only:mb-3 only:mt-2">
            {isRaw ? (
              <SyntaxHighlight
                content={JSON.stringify(message.tool_calls, null, 2)}
                language="json"
                className="bg-transparent"
              />
            ) : (
              <ToolsViewer toolCalls={message.tool_calls} />
            )}
          </div>
        )}
    </div>
  );
};

export const LLMMessageView = ({
  value,
  isRaw,
}: {
  value: unknown;
  isRaw?: boolean;
}) => {
  const parsed = parseLLMSpanPart(value);

  if (!parsed) {
    return (
      <div className="px-2 py-1 text-xs text-bad-800">
        LLM messages could not be parsed
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-3 px-2 pb-3 pt-1">
      {parsed.map((message, idx) => (
        <div className="border-l-2 pl-2 border-primary-300" key={idx}>
          <CollapsibleSection
            title={message.role}
            defaultCollapsed={message.role === "tool"}
            className="mb-0 gap-1 text-xs capitalize text-primary-600"
          >
            <LLMMessage message={message} isRaw={isRaw} />
          </CollapsibleSection>
        </div>
      ))}
    </div>
  );
};

export const MessageBubble = ({
  message,
  toolDefinitions,
  isLoading = false,
  onDelete,
  onAdd,
  extraMessagesPath,
}: {
  message: LLMMessageType | null;
  toolDefinitions: Map<string, string>;
  isLoading?: boolean;
  onDelete?: () => void;
  onAdd?: () => void;
  extraMessagesPath?: string;
}) => {
  if (!message) return null;

  let displayRole: string = message.role;
  let toolName: string | undefined;
  if (
    message.role === "tool" &&
    "tool_call_id" in message &&
    message.tool_call_id
  ) {
    toolName = toolDefinitions.get(message.tool_call_id);
    if (toolName) {
      displayRole = "Tool";
    }
  }

  const isDeemphasized = !!extraMessagesPath;

  return (
    <div
      className={cn("flex-col flex items-start mb-3 gap-1 mr-3 group", {
        "items-end mr-0 ml-3": message.role === "user",
        "opacity-70": isDeemphasized,
      })}
    >
      <span>
        <span className="text-xs capitalize text-primary-500">
          {displayRole}
        </span>
        {extraMessagesPath && (
          <>
            <code className="ml-1 text-xs text-primary-500">
              {"("}
              {extraMessagesPath}
              {")"}
            </code>
          </>
        )}
      </span>
      <div
        className={cn(
          "mb-2 inline-block rounded-xl px-3 text-sm bg-primary-100 group [&_.bg-primary-50]:!bg-transparent relative",
          {
            "bg-accent-50": message.role === "user",
            "bg-amber-50 dark:bg-amber-950 py-2": message.role === "tool",
            "border bg-primary-100/50 border-dashed": isDeemphasized,
            "bg-accent-50/50": isDeemphasized && message.role === "user",
            "bg-amber-50/50 dark:bg-amber-950/50":
              isDeemphasized && message.role === "tool",
          },
        )}
      >
        {onAdd && (
          <BasicTooltip tooltipContent="Add message to prompt">
            <Button
              onClick={onAdd}
              variant="ghost"
              transparent
              size="xs"
              className={cn(
                "absolute -bottom-7 right-6 truncate opacity-0 transition-opacity text-primary-500 group-hover:opacity-100",
                {
                  "left-6 right-auto": message.role !== "user",
                },
              )}
              Icon={Plus}
              aria-label="Add message to prompt"
            />
          </BasicTooltip>
        )}
        {onDelete && (
          <BasicTooltip tooltipContent="Delete message">
            <Button
              onClick={onDelete}
              variant="ghost"
              transparent
              size="xs"
              className={cn(
                "absolute -bottom-7 right-0 opacity-0 transition-opacity text-primary-500 group-hover:opacity-100",
                {
                  "left-0 right-auto": message.role !== "user",
                },
              )}
              Icon={Trash}
              aria-label="Delete message"
            />
          </BasicTooltip>
        )}
        {isLoading ? (
          <LoadingMessage />
        ) : toolName ? (
          <CollapsibleSection
            defaultCollapsed
            className="bg-transparent hover:bg-amber-100 dark:hover:bg-amber-900"
            title={
              <div className="flex items-center gap-2 text-xs font-medium">
                <Bolt className="size-3" />
                {toolName}
              </div>
            }
          >
            <LLMMessage message={message} hideCopyButton />
          </CollapsibleSection>
        ) : (
          <LLMMessage message={message} hideCopyButton />
        )}
      </div>
    </div>
  );
};

const LoadingMessage = () => {
  return (
    <div className="flex gap-1 p-2 py-3">
      {[1, 2, 3].map((i) => (
        <motion.div
          key={i}
          className="size-1.5 rounded-full bg-primary-500"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [1, 0.5, 1],
          }}
          transition={{
            duration: 1,
            repeat: Infinity,
            delay: i * 0.2,
          }}
        />
      ))}
    </div>
  );
};
