import {
  SpanSpec,
  Span,
  makeSpan,
  reconstructAttributes,
  FieldStats,
} from "./attributes";
import { braintrustSpanSpec } from "./braintrust";
import { genAISpanSpec } from "./gen-ai";
import { traceloopSpanSpec } from "./traceloop-sdk";
import { vercelSpanSpec } from "./vercel-ai-sdk";
import { llamaIndexSpanSpec } from "./llama-index";

// - For `input` and `output`, we consume the specs in the order they're listed here,
//   using the first non-empty result we find.
// - For `metadata` and `metrics`, we consume every spec and deep-merge the results.
const SPAN_SPECS: Record<string, SpanSpec> = {
  braintrust: braintrustSpanSpec,
  genAI: genAISpanSpec,
  vercel: vercelSpanSpec,
  llamaIndex: llamaIndexSpanSpec,
  traceloop: traceloopSpanSpec,
};

export function convertAttributesToSpan(
  flatAttributes: Record<string, unknown>,
  events?: unknown[],
): { span: Span; keysToDelete: Set<string>; fieldStats: FieldStats } {
  const attributes = reconstructAttributes(flatAttributes);
  return makeSpan({
    attributes,
    events,
    specs: SPAN_SPECS,
    debug: !!process.env.BRAINTRUST_OTEL_DEBUG,
  });
}

// Re-export types and utilities
export { FieldStats };
