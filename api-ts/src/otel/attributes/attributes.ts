import { z } from "zod";
import { set } from "lodash";
import {
  isEmpty,
  isObject,
  mergeDicts,
  SpanTypeAttribute,
} from "@braintrust/core";
import { Message } from "@braintrust/core/typespecs";
import { translateParams } from "@braintrust/proxy/schema";

export const spanSchema = z.object({
  input: z.unknown().optional(),
  output: z.unknown().optional(),
  metadata: z.record(z.unknown()).optional(),
  metrics: z.record(z.number()).optional(),
  span_attributes: z.record(z.string()).optional(),
  scores: z.record(z.union([z.number(), z.null()])).optional(),
  expected: z.unknown().optional(),
  tags: z.array(z.string()).optional(),
});

export type Span = z.infer<typeof spanSchema>;

export function reconstructAttributes(
  flatAttributes: Record<string, unknown>,
): unknown {
  const attributes = {};
  for (const [key, value] of Object.entries(flatAttributes)) {
    // TODO(austin): This is a hack to work around the fact that the Vercel AI SDK
    // puts the schema name under `ai.schema.name` which "conflicts" with the actual
    // JSON schema at `ai.schema`. Maybe they'll change it, but for now just rename
    // the attribute.
    const btKey =
      key === "ai.schema.name" && "ai.schema" in flatAttributes
        ? "ai.schema_name"
        : key;
    set(attributes, btKey, value);
  }
  return attributes;
}

function sanitizeMetrics(
  metrics: Record<string, unknown>,
): Record<string, number> | undefined {
  const ret: Record<string, number> = {};
  for (const [key, value] of Object.entries(metrics)) {
    if (typeof value === "number") {
      ret[key] = value;
    }
  }
  return ret;
}

function sanitizeSpanAttrs(
  attrs: Record<string, unknown>,
): Record<string, string> {
  const ret: Record<string, string> = {};
  for (const [key, value] of Object.entries(attrs)) {
    if (typeof value === "string") {
      ret[key] = value;
    }
  }
  return ret;
}

function postprocessMetrics(
  metrics: Record<string, unknown>,
): Record<string, number> | undefined {
  const ret = sanitizeMetrics(metrics);
  if (
    ret &&
    isEmpty(ret.tokens) &&
    typeof ret.prompt_tokens === "number" &&
    typeof ret.completion_tokens === "number"
  ) {
    ret.tokens = ret.prompt_tokens + ret.completion_tokens;
  }
  return ret;
}

export type MessageField =
  | {
      isLLM: true;
      data: Message[];
    }
  | {
      isLLM: false;
      data: unknown;
    };

// Discriminated union for processing results
type NotHandledResult = {
  handled: false;
};

type HandledResult<T> = {
  handled: true;
  value: NonNullable<T>; // Prohibit undefined/null values
  deleteKeys: Set<string>; // Mandatory to reduce type checks
};

export type SpecResult<T> = NotHandledResult | HandledResult<T>;

// Field processing statistics by spec name
export class FieldStats {
  private stats: Record<string, { ok: number; errs: number }> = {};

  // Increment ok counter for a spec
  incrOk(spec: string, count: number = 1): void {
    if (!this.stats[spec]) {
      this.stats[spec] = { ok: 0, errs: 0 };
    }
    this.stats[spec].ok += count;
  }

  // Increment error counter for a spec
  incrErr(spec: string, count: number = 1): void {
    if (!this.stats[spec]) {
      this.stats[spec] = { ok: 0, errs: 0 };
    }
    this.stats[spec].errs += count;
  }

  // Merge another FieldStats into this one (mutates this)
  merge(other: FieldStats): void {
    for (const [specName, stats] of Object.entries(other.stats)) {
      if (stats.ok === 0 && stats.errs === 0) {
        continue;
      }
      if (!this.stats[specName]) {
        this.stats[specName] = { ok: 0, errs: 0 };
      }
      this.stats[specName].ok += stats.ok;
      this.stats[specName].errs += stats.errs;
    }
  }

  // Get the underlying stats object for serialization/logging
  toObject(): Record<string, { ok: number; errs: number }> {
    // Only return specs with non-zero values
    const result: Record<string, { ok: number; errs: number }> = {};
    for (const [specName, stats] of Object.entries(this.stats)) {
      if (stats.ok > 0 || stats.errs > 0) {
        result[specName] = { ...stats };
      }
    }
    return result;
  }

  // Custom JSON serialization for snapshots/tests
  toJSON(): Record<string, { ok: number; errs: number }> {
    return this.toObject();
  }
}

// Generic result type for make functions
type FieldResult<T> = {
  value: T;
  keysToDelete: Set<string>;
  fieldStats: FieldStats;
};

// Updated spec types using SpecResult
type MessageFieldSpec = (
  source: unknown,
  events?: unknown[],
) => SpecResult<MessageField>;
type MapSpec = (
  source: unknown,
  events?: unknown[],
) => SpecResult<Record<string, unknown>>;
type TagsSpec = (source: unknown, events?: unknown[]) => SpecResult<string[]>;
type AnySpec = (source: unknown, events?: unknown[]) => SpecResult<unknown>;

// Helper constants and functions for creating SpecResults
export const notHandled: NotHandledResult = { handled: false };

export function handled<T>(
  value: NonNullable<T>,
  deleteKeys: string[] = [],
): HandledResult<T> {
  return { handled: true, value, deleteKeys: new Set(deleteKeys) };
}

export interface SpanSpec {
  input?: MessageFieldSpec;
  output?: MessageFieldSpec;
  metadata?: MapSpec;
  metrics?: MapSpec;
  span_attributes?: MapSpec;
  scores?: MapSpec;
  expected?: AnySpec;
  tags?: TagsSpec;
}

type MakeSpanArgs = {
  attributes: unknown;
  events?: unknown[];
  specs: Record<string, SpanSpec>;
  debug?: boolean;
};

type MessageFields = "input" | "output";

function makeMessagesField({
  attributes,
  events,
  specs,
  field,
  debug,
}: MakeSpanArgs & { field: MessageFields }): FieldResult<
  MessageField | undefined
> {
  const fieldStats = new FieldStats();

  // Try to process the span with each spec and use the first valid result.
  for (const [name, spec] of Object.entries(specs)) {
    const fieldSpec = spec[field];
    if (!fieldSpec) continue;

    try {
      const result = fieldSpec(attributes, events);
      if (result.handled) {
        fieldStats.incrOk(name);
        return {
          value: result.value,
          keysToDelete: result.deleteKeys,
          fieldStats,
        };
      }
    } catch (error) {
      fieldStats.incrErr(name);
      if (debug) {
        const log = { error, attributes, events, name, field };
        console.log(JSON.stringify(log, null, 2));
      }
    }
  }
  return { value: undefined, keysToDelete: new Set(), fieldStats };
}

type MapFields = "metadata" | "metrics" | "span_attributes" | "scores";

function makeMapField({
  attributes,
  events,
  specs,
  field,
  debug,
}: MakeSpanArgs & { field: MapFields }): FieldResult<Record<string, unknown>> {
  // Reconstruct as much as we can from each spec and deep-merge them all together.
  const result: Record<string, unknown> = {};
  const keysToDelete = new Set<string>();
  const fieldStats = new FieldStats();

  for (const [name, spec] of Object.entries(specs)) {
    const mapSpec = spec[field];
    if (!mapSpec) continue;

    try {
      const specResult = mapSpec(attributes, events);
      if (specResult.handled && !isEmpty(specResult.value)) {
        fieldStats.incrOk(name);
        for (const key of specResult.deleteKeys) {
          keysToDelete.add(key);
        }
        const sanitizedPartialResult = Object.fromEntries(
          Object.entries(specResult.value).filter(
            ([_, value]) => !isEmpty(value),
          ),
        );
        mergeDicts(result, sanitizedPartialResult);
      }
    } catch (error) {
      fieldStats.incrErr(name);
      if (debug) {
        const log = { error, attributes, events, name, field };
        console.log(JSON.stringify(log, null, 2));
      }
    }
  }
  return { value: result, keysToDelete, fieldStats };
}

function makeAnyField({
  attributes,
  events,
  specs,
  field,
  debug,
}: MakeSpanArgs & { field: "expected" }): FieldResult<unknown | undefined> {
  const keysToDelete = new Set<string>();
  const fieldStats = new FieldStats();

  for (const [name, spec] of Object.entries(specs)) {
    if (!spec[field]) continue;
    const fieldSpec: AnySpec = spec[field];

    try {
      const specResult = fieldSpec(attributes, events);
      if (specResult.handled) {
        fieldStats.incrOk(name);
        for (const key of specResult.deleteKeys) {
          keysToDelete.add(key);
        }
        return { value: specResult.value, keysToDelete, fieldStats };
      }
    } catch (error) {
      fieldStats.incrErr(name);
      if (debug) {
        const log = { error, attributes, events, name, field };
        console.log(JSON.stringify(log, null, 2));
      }
    }
  }
  return { value: undefined, keysToDelete, fieldStats };
}

function makeTagsField({
  attributes,
  events,
  specs,
  field,
  debug,
}: MakeSpanArgs & { field: "tags" }): FieldResult<string[] | undefined> {
  const keysToDelete = new Set<string>();
  const fieldStats = new FieldStats();

  for (const [name, spec] of Object.entries(specs)) {
    if (!spec[field]) continue;
    const fieldSpec: TagsSpec = spec[field];

    try {
      const specResult = fieldSpec(attributes, events);
      if (specResult.handled) {
        fieldStats.incrOk(name);
        for (const key of specResult.deleteKeys) {
          keysToDelete.add(key);
        }
        return { value: specResult.value, keysToDelete, fieldStats };
      }
    } catch (error) {
      fieldStats.incrErr(name);
      if (debug) {
        const log = { error, attributes, events, name, field };
        console.log(JSON.stringify(log, null, 2));
      }
    }
  }
  return { value: undefined, keysToDelete, fieldStats };
}

export function makeSpan(args: MakeSpanArgs): {
  span: Span;
  keysToDelete: Set<string>;
  fieldStats: FieldStats;
} {
  const keysToDelete = new Set<string>();

  // Process fields using table-driven approach
  const results = {
    input: makeMessagesField({ ...args, field: "input" }),
    output: makeMessagesField({ ...args, field: "output" }),
    metadata: makeMapField({ ...args, field: "metadata" }),
    metrics: makeMapField({ ...args, field: "metrics" }),
    span_attributes: makeMapField({ ...args, field: "span_attributes" }),
    scores: makeMapField({ ...args, field: "scores" }),
    expected: makeAnyField({ ...args, field: "expected" }),
    tags: makeTagsField({ ...args, field: "tags" }),
  };

  // Collect all keys to delete and aggregate field stats
  const fieldStats = new FieldStats();
  for (const result of Object.values(results)) {
    for (const key of result.keysToDelete) {
      keysToDelete.add(key);
    }

    // Aggregate field stats using the merge method
    fieldStats.merge(result.fieldStats);
  }

  // Extract values
  const input = results.input.value;
  const output = results.output.value;
  const metadata = results.metadata.value;
  const metrics = results.metrics.value;
  const attrs = results.span_attributes.value;
  const scores = results.scores.value;
  const expected = results.expected.value;
  const tags = results.tags.value;

  // set a default type if not submitted.
  // NOTE[matt] I'm not sure if makes sense to label random spans tasks, but
  // I'm keeping it here until I understand this more
  if (!attrs.type) {
    const spanType =
      input?.isLLM || output?.isLLM
        ? SpanTypeAttribute.LLM
        : SpanTypeAttribute.TASK;
    attrs.type = spanType;
  }

  const span: Span = {
    input: input?.data,
    output: output?.data,
    metadata,
    metrics: postprocessMetrics(metrics),
    span_attributes: sanitizeSpanAttrs(attrs),
  };

  if (Object.keys(scores).length > 0) {
    span.scores = sanitizeScores(scores);
  }

  if (expected !== undefined) {
    span.expected = expected;
  }

  if (tags !== undefined) {
    span.tags = tags;
  }

  return {
    span,
    keysToDelete,
    fieldStats,
  };
}

function sanitizeScores(
  scores: Record<string, unknown>,
): Record<string, number | null> {
  const ret: Record<string, number | null> = {};
  for (const [key, value] of Object.entries(scores)) {
    if (typeof value === "number") {
      ret[key] = value;
    }
  }
  return ret;
}

export function mustParseJson(value: unknown): unknown {
  if (typeof value !== "string") {
    throw new Error("Cannot deserialize non-string value");
  }
  return JSON.parse(value);
}

export function deserializeIfString(value: unknown): unknown {
  return typeof value === "string" ? JSON.parse(value) : value;
}

export function translateModelParams(params: unknown): Record<string, unknown> {
  if (!isObject(params)) {
    return {};
  }
  return translateParams("openai", params);
}
