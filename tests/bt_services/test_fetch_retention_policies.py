import unittest
import uuid

import braintrust

from tests.braintrust_app_test_base import LOCAL_API_URL, LOCAL_APP_URL
from tests.bt_services.test_automations import AutomationTestBase


class FetchRetentionPoliciesTest(AutomationTestBase):
    """
    This test ensures that both implementations of retention policy lookups produce identical results:
    - /retention/policies (TypeScript implementation)
    - /brainstore/retention/policies (Brainstore implementation via TypeScript passthrough)
    """

    def setUp(self):
        super().setUp()
        self.bt_data_plane_service_token = self.ensureDataPlaneServiceTokenExists()

    def _make_brainstore_request(self, objects, fail_unknown_objects=False):
        """Make request to brainstore via TypeScript API passthrough /brainstore/retention/policies endpoint"""
        json_data = dict(objects=objects, fail_unknown_objects=fail_unknown_objects)
        response = self.run_request(
            "post",
            f"{LOCAL_API_URL}/brainstore/retention/fetch-policies",
            json=json_data,
            headers={"Authorization": f"Bearer {self.bt_data_plane_service_token}"},
        )
        return response

    def _make_typescript_request(self, objects, fail_unknown_objects=False):
        """Make request to data plane API /retention/policies endpoint (TypeScript implementation)"""
        json_data = dict(objects=objects, fail_unknown_objects=fail_unknown_objects)
        response = self.run_request(
            "post",
            f"{LOCAL_API_URL}/retention/fetch-policies",
            json=json_data,
            headers={"Authorization": f"Bearer {self.bt_data_plane_service_token}"},
        )
        return response

    def _compare_policy_responses(self, ts_data, bs_data, test_name):
        assert ts_data == bs_data, f"{test_name}: deep equality expected"

    def verify_response_equivalence(self, ts_response, bs_response, test_name):
        if hasattr(ts_response, "status_code") and hasattr(bs_response, "status_code"):
            self.assertEqual(
                ts_response.status_code, bs_response.status_code, f"{test_name}: HTTP status codes should match"
            )

        if ts_response.status_code == 200 and bs_response.status_code == 200:
            # For successful responses, compare the actual policy data.
            try:
                ts_data = ts_response.json() if hasattr(ts_response, "json") else ts_response
                bs_data = bs_response.json() if hasattr(bs_response, "json") else bs_response
            except Exception as e:
                self.fail(f"{test_name}: Failed to parse successful JSON responses: {e}")
            assert ts_data == bs_data, f"{test_name}: Policy data mismatch!\napi-ts: {ts_data}\nBrainstore: {bs_data}"
        else:
            # For error responses, we only verify they both failed with same status code,
            # since error message formats can differ between the APIs.
            self.assertEqual(
                ts_response.status_code,
                bs_response.status_code,
                f"{test_name}: Both APIs should return the same error status code",
            )

    def test_basic_policy_resolution_comparison(self):
        project = self.run_request("post", f"{LOCAL_API_URL}/v1/project", json={"name": "test-project"}).json()

        objects = [{"object_type": "project_logs", "object_id": project["id"]}]
        self._create_automation(
            project["id"],
            dict(
                name="test-retention",
                description="Test retention policy",
                config=dict(
                    event_type="retention",
                    object_type="project_logs",
                    retention_days=30,
                ),
            ),
        )

        ts_response = self._make_typescript_request(objects)
        bs_response = self._make_brainstore_request(objects)
        self.verify_response_equivalence(ts_response, bs_response, "basic_policy_resolution")

    def test_multiple_object_types_comparison(self):
        raise unittest.SkipTest("TODO(object_type): implement experiment and dataset object types")

        project = self.run_request("post", f"{LOCAL_API_URL}/v1/project", json={"name": "test-project"}).json()
        experiment = self.run_request(
            "post", f"{LOCAL_API_URL}/v1/experiment", json={"project_id": project["id"], "name": "test-experiment"}
        ).json()
        dataset = self.run_request(
            "post", f"{LOCAL_API_URL}/v1/dataset", json={"project_id": project["id"], "name": "test-dataset"}
        ).json()

        # Create automations and store them by object type
        automations_by_type = {}
        automations_by_type["experiment"] = self._create_automation(
            project["id"],
            dict(
                name="experiment-policy",
                description="Experiment retention policy",
                config=dict(
                    event_type="retention",
                    object_type="experiment",
                    retention_days=30,
                ),
            ),
        ).json()
        automations_by_type["dataset"] = self._create_automation(
            project["id"],
            dict(
                name="dataset-policy",
                description="Dataset retention policy",
                config=dict(
                    event_type="retention",
                    object_type="dataset",
                    retention_days=90,
                ),
            ),
        ).json()

        nonexistent_object_id = str(uuid.uuid4())

        # Create test objects organized by ID
        test_objects = {
            f"experiment:{experiment['id']}": {
                "object_type": "experiment",
                "object_id": experiment["id"],
                "expected_retention_days": 30,
            },
            f"dataset:{dataset['id']}": {
                "object_type": "dataset",
                "object_id": dataset["id"],
                "expected_retention_days": 90,
            },
            f"project_logs:{project['id']}": {
                "object_type": "project_logs",
                "object_id": project["id"],
                "expected_retention_days": None,
            },
            f"experiment:{nonexistent_object_id}": {
                "object_type": "experiment",
                "object_id": nonexistent_object_id,
                "expected_retention_days": None,
            },
        }
        objects = [{"object_type": obj["object_type"], "object_id": obj["object_id"]} for obj in test_objects.values()]

        ts_response = self._make_typescript_request(objects)
        bs_response = self._make_brainstore_request(objects)
        self.verify_response_equivalence(ts_response, bs_response, "multiple_object_types")

        # In the new format, only objects WITH policies are included in the response
        # So we expect 2 objects (experiment and dataset), not 4
        self.assertEqual(len(ts_response.json()), 2)
        self.assertEqual(len(bs_response.json()), 2)

        # Verify against the known automations we created
        ts_by_id = ts_response.json()

        # Verify each object has the expected policy
        for obj_id, expected in test_objects.items():
            policy = ts_by_id.get(obj_id)

            if expected["expected_retention_days"] is None:
                # Objects without policies are omitted from the response entirely
                self.assertIsNone(policy, f"Object {obj_id} should be omitted from response (no policy)")
            else:
                # Objects with policies should be present in the response
                self.assertIsNotNone(policy, f"Object {obj_id} should be in response with policy")
                self.assertIsNotNone(policy["automation_id"], f"Object {obj_id} should have an automation ID")
                self.assertEqual(
                    policy["retention_days"],
                    expected["expected_retention_days"],
                    f"Object {obj_id} should have {expected['expected_retention_days']}-day retention",
                )

    def test_comprehensive_schema_validation_across_orgs(self):
        raise unittest.SkipTest("TODO(object_type): implement experiment and dataset object types")

        """Test that both endpoints return EXACTLY the same schema across complex scenarios"""
        N_orgs = 3
        N_projects = 3
        N_objects = 10

        orgs = []
        projects = []
        all_objects = []
        automations = []

        # Organize objects by ID for easier lookup
        objects_by_id = {}
        automations_by_project = {}

        for i in range(N_orgs):
            # Create a new org
            org_id, org_name = self.createOrg()
            orgs.append({"id": org_id, "name": org_name})
            self.addUserToOrg(self.user_id, org_id)
            print(f"{i}: Created org {org_name}")

            user_api_key = self.createUserOrgApiKey(self.user_id, org_id)

            for j in range(N_projects):
                # Create project in this org
                project = self.run_request(
                    "post",
                    f"{LOCAL_API_URL}/v1/project",
                    json={"name": f"test-project-{i}", "org_name": org_name},
                    headers={"Authorization": f"Bearer {user_api_key}"},
                ).json()
                projects.append({"org_id": org_id, "project": project})
                print(f"{i}-{j}: Created project {project['name']}")

                # Create experiments
                for k in range(N_objects):
                    exp = self.run_request(
                        "post",
                        f"{LOCAL_API_URL}/v1/experiment",
                        json={"project_id": project["id"], "name": f"exp-{i}-{j}-{k}"},
                    ).json()
                    obj_data = {
                        "object_type": "experiment",
                        "object_id": exp["id"],
                        "project_id": project["id"],
                        "org_index": i,
                    }
                    all_objects.append(obj_data)
                    objects_by_id[exp["id"]] = obj_data
                    print(f"{i}-{j}={k}: Created experiment {exp['name']}")

                # Create datasets
                for k in range(N_objects):
                    dataset = self.run_request(
                        "post",
                        f"{LOCAL_API_URL}/v1/dataset",
                        json={"project_id": project["id"], "name": f"dataset-{i}-{j}-{k}"},
                    ).json()
                    obj_data = {
                        "object_type": "dataset",
                        "object_id": dataset["id"],
                        "project_id": project["id"],
                        "org_index": i,
                    }
                    all_objects.append(obj_data)
                    objects_by_id[dataset["id"]] = obj_data
                    print(f"{i}-{j}={k}: Created dataset {dataset['name']}")

                # Add project_logs objects
                braintrust.login(org_name=org_name, app_url=LOCAL_APP_URL, api_key=user_api_key, force_login=True)
                logger = braintrust.init_logger(project["name"], org_name=org_name)
                for k in range(N_objects):
                    id = logger.log(input={"project_id": project["id"], "name": f"exp-{i}-{j}-{k}"}, output=str(k))
                    obj_data = {
                        "object_type": "project_logs",
                        "object_id": project["id"],
                        "project_id": project["id"],
                        "log_id": id,
                        "org_index": i,
                    }
                    all_objects.append(obj_data)
                    # For project_logs, we use project ID as the key since multiple logs share the same project
                    objects_by_id[project["id"]] = obj_data
                    print(f"{i}-{j}={k}: Created log {id}")

                # Create prompts (NOT supported for retention)
                for k in range(N_objects):
                    prompt = self.run_request(
                        "post",
                        f"{LOCAL_API_URL}/v1/prompt",
                        json={
                            "project_id": project["id"],
                            "name": f"prompt-{i}-{j}-{k}",
                            "slug": f"prompt-{i}-{j}={k}",
                            "prompt_data": {
                                "prompt": {"type": "chat", "messages": [{"role": "user", "content": "Test prompt"}]},
                                "options": {"model": "gpt-3.5-turbo"},
                            },
                        },
                    ).json()
                    print(f"{i}-{j}={k}: Created prompt {prompt['name']}")

                # Create various automation types for this project
                if project["id"] not in automations_by_project:
                    automations_by_project[project["id"]] = {}

                # Retention policy for experiments (different days per project)
                exp_retention = self._create_automation(
                    project["id"],
                    dict(
                        name=f"exp-retention-project-{i}",
                        description=f"Experiment retention for project {i}",
                        config=dict(event_type="retention", object_type="experiment", retention_days=30 + i * 10),
                    ),
                ).json()
                automations.append({"project_id": project["id"], "automation": exp_retention["project_automation"]})
                automations_by_project[project["id"]]["experiment"] = {
                    "automation": exp_retention["project_automation"],
                    "retention_days": 30 + i * 10,
                }
                print(f"{i}-{j}: Created retention {exp_retention['project_automation']['name']}")

                # Retention policy for datasets (different days)
                dataset_retention = self._create_automation(
                    project["id"],
                    dict(
                        name=f"dataset-retention-project-{i}",
                        description=f"Dataset retention for project {i}",
                        config=dict(event_type="retention", object_type="dataset", retention_days=60 + i * 15),
                    ),
                ).json()
                automations.append(
                    {"project_id": project["id"], "automation": dataset_retention["project_automation"]}
                )
                automations_by_project[project["id"]]["dataset"] = {
                    "automation": dataset_retention["project_automation"],
                    "retention_days": 60 + i * 15,
                }
                print(f"{i}-{j}: Created retention {dataset_retention['project_automation']['name']}")

                # Retention policy for project_logs (only for project 1)
                if i == 1:
                    logs_retention = self._create_automation(
                        project["id"],
                        dict(
                            name=f"logs-retention-project-{i}",
                            description=f"Logs retention for project {i}",
                            config=dict(event_type="retention", object_type="project_logs", retention_days=180),
                        ),
                    ).json()
                    automations.append(
                        {"project_id": project["id"], "automation": logs_retention["project_automation"]}
                    )
                    automations_by_project[project["id"]]["project_logs"] = {
                        "automation": logs_retention["project_automation"],
                        "retention_days": 180,
                    }
                    print(f"{i}-{j}: Created retention {logs_retention['project_automation']['name']}")

                if i == 2:
                    # Create a non-retention automation (webhook) to ensure it's NOT returned
                    webhook_automation = self._create_automation(
                        project["id"],
                        dict(
                            name=f"webhook-project-{i}",
                            description=f"Webhook automation for project {i}",
                            config=dict(
                                event_type="logs",
                                btql_filter="input.foo = 12",
                                action=dict(type="webhook", url=f"https://example.com/webhook-{i}"),
                                interval_seconds=60 * 5,
                            ),
                        ),
                    ).json()
                    print(f"{i}-{j}: Created webhook automation {webhook_automation['project_automation']['name']}")

        # Add some non-existent object IDs
        for _ in range(3):
            nonexistent_exp_id = str(uuid.uuid4())
            obj_data = {"object_type": "experiment", "object_id": nonexistent_exp_id, "project_id": "nonexistent"}
            all_objects.append(obj_data)
            objects_by_id[nonexistent_exp_id] = obj_data

            nonexistent_dataset_id = str(uuid.uuid4())
            obj_data = {"object_type": "dataset", "object_id": nonexistent_dataset_id, "project_id": "nonexistent"}
            all_objects.append(obj_data)
            objects_by_id[nonexistent_dataset_id] = obj_data

        # Prepare request objects (without project_id)
        request_objects = [{"object_type": obj["object_type"], "object_id": obj["object_id"]} for obj in all_objects]

        # Make requests to both endpoints
        ts_response = self._make_typescript_request(request_objects)
        bs_response = self._make_brainstore_request(request_objects)

        self.verify_response_equivalence(ts_response, bs_response, "comprehensive_schema_validation_across_orgs")

        ts_data = ts_response.json()
        bs_data = bs_response.json()

        # Verify retention policies resolved correctly using our organized dictionaries
        for obj_key, policy in ts_data.items():
            # Parse the object key: "object_type:object_id"
            obj_type, obj_id = obj_key.split(":", 1)

            # Look up the object metadata
            obj_metadata = objects_by_id.get(obj_id)
            obj_desc = f"{obj_type}:{obj_id}"

            # Special handling for objects that should be skipped
            if obj_type == "project_prompts":
                # Prompts should not be considered
                assert False, f"{obj_desc}: Prompts should not appear in response"

            project_id = obj_metadata["project_id"]

            if project_id == "nonexistent":
                # Non-existent objects should not appear in response
                assert False, f"{obj_desc}: Non-existent objects should not appear in response"
            elif obj_type == "experiment":
                # All projects have experiment retention
                if project_id in automations_by_project and "experiment" in automations_by_project[project_id]:
                    expected_days = automations_by_project[project_id]["experiment"]["retention_days"]
                    self.assertIsNotNone(policy, f"{obj_desc}: Experiment should have retention policy")
                    self.assertEqual(
                        policy["retention_days"],
                        expected_days,
                        f"{obj_desc}: Experiment retention days",
                    )
                else:
                    assert False, "Should have found experiment retention policy"
            elif obj_type == "dataset":
                # All projects have dataset retention
                if project_id in automations_by_project and "dataset" in automations_by_project[project_id]:
                    expected_days = automations_by_project[project_id]["dataset"]["retention_days"]
                    self.assertIsNotNone(policy, f"{obj_desc}: Dataset should have retention policy")
                    self.assertEqual(
                        policy["retention_days"],
                        expected_days,
                        f"{obj_desc}: Dataset retention days",
                    )
                else:
                    assert False, "Should have found dataset retention policy"
            elif obj_type == "project_logs":
                if project_id in automations_by_project and "project_logs" in automations_by_project[project_id]:
                    # Only org index 1 has project_logs retention
                    assert (
                        obj_metadata["org_index"] == 1
                    ), "Expected project logs retention policy for just org_index 1"
                    expected_days = automations_by_project[project_id]["project_logs"]["retention_days"]
                    self.assertIsNotNone(policy, f"{obj_desc}: Project logs should have retention policy")
                    self.assertEqual(policy["retention_days"], expected_days, f"{obj_desc}: Logs retention days")
                else:
                    # If no policy exists for project_logs, object shouldn't be in response
                    assert False, f"{obj_desc}: Project logs without policy should not appear in response"

        # Verify no webhook automations were included
        for obj_key, policy in ts_data.items():
            if policy is not None:
                self.assertIn(
                    policy["automation_id"],
                    [a["automation"]["id"] for a in automations],
                    "Only retention automation IDs should be returned",
                )

    def test_cross_project_isolation(self):
        """Test that retention policies are isolated across projects"""
        # Create two projects
        project1 = self.run_request("post", f"{LOCAL_API_URL}/v1/project", json={"name": "project-1"}).json()
        project2 = self.run_request("post", f"{LOCAL_API_URL}/v1/project", json={"name": "project-2"}).json()

        # Create policy only for project1
        automation = self._create_automation(
            project1["id"],
            dict(
                name="project1-policy",
                description="Project 1 retention policy",
                config=dict(
                    event_type="retention",
                    object_type="project_logs",
                    retention_days=30,
                ),
            ),
        ).json()

        # Test objects from both projects
        objects = [
            {"object_type": "project_logs", "object_id": project1["id"]},  # Should have policy
            {"object_type": "project_logs", "object_id": project2["id"]},  # Should NOT have policy
        ]

        ts_response = self._make_typescript_request(objects)
        bs_response = self._make_brainstore_request(objects)

        self.verify_response_equivalence(ts_response, bs_response, "cross_project_isolation")

        # Verify isolation: exp1 has policy, exp2 doesn't.
        ts_data = ts_response.json()

        # In the new format, only objects WITH policies are included
        # So we expect only project1 to be in the response, project2 should be omitted
        project1_key = f"project_logs:{project1['id']}"
        project2_key = f"project_logs:{project2['id']}"

        self.assertIn(project1_key, ts_data, "project1 should have a policy and be in response")
        self.assertNotIn(project2_key, ts_data, "project2 should have no policy and be omitted from response")

        # Verify project1's policy details
        project1_policy = ts_data[project1_key]
        self.assertIsNotNone(project1_policy, "project1 should have a policy")
        self.assertEqual(project1_policy["retention_days"], 30)

    def test_cross_org_boundary(self):
        """Test that retention policies from one org don't affect objects in another org"""
        # Create two orgs
        org1_id, org1_name = self.createOrg()
        org2_id, org2_name = self.createOrg()

        # Add user to both orgs
        self.addUserToOrg(self.user_id, org1_id)
        self.addUserToOrg(self.user_id, org2_id)

        # Create API keys for both orgs
        org1_api_key = self.createUserOrgApiKey(self.user_id, org1_id)
        org2_api_key = self.createUserOrgApiKey(self.user_id, org2_id)

        # Create projects in both orgs
        project1 = self.run_request(
            "post",
            f"{LOCAL_API_URL}/v1/project",
            json={"name": "project-org1", "org_name": org1_name},
            headers={"Authorization": f"Bearer {org1_api_key}"},
        ).json()

        project2 = self.run_request(
            "post",
            f"{LOCAL_API_URL}/v1/project",
            json={"name": "project-org2", "org_name": org2_name},
            headers={"Authorization": f"Bearer {org2_api_key}"},
        ).json()

        # Create retention policy only in org1
        self._create_automation(
            project1["id"],
            dict(
                name="org1-retention",
                description="Org1 retention policy",
                config=dict(
                    event_type="retention",
                    object_type="project_logs",
                    retention_days=30,
                ),
            ),
            api_key=org1_api_key,
        )

        # Query both projects
        objects = [
            {"object_type": "project_logs", "object_id": project1["id"]},
            {"object_type": "project_logs", "object_id": project2["id"]},
        ]

        # TODO(object_type): Enable for experiments and datasets
        ## Create experiments in both projects
        # exp1 = self.run_request(
        #    "post",
        #    f"{LOCAL_API_URL}/v1/experiment",
        #    json={"project_id": project1["id"], "name": "exp-org1"},
        #    headers={"Authorization": f"Bearer {org1_api_key}"},
        # ).json()

        # exp2 = self.run_request(
        #    "post",
        #    f"{LOCAL_API_URL}/v1/experiment",
        #    json={"project_id": project2["id"], "name": "exp-org2"},
        #    headers={"Authorization": f"Bearer {org2_api_key}"},
        # ).json()

        # # Create retention policy only in org1
        # self._create_automation(
        #     project1["id"],
        #     dict(
        #         name="org1-retention",
        #         description="Org1 retention policy",
        #         config=dict(
        #             event_type="retention",
        #             object_type="experiment",
        #             retention_days=30,
        #         ),
        #     ),
        #     api_key=org1_api_key,
        # )

        # # Query both experiments
        # objects = [
        #     {"object_type": "experiment", "object_id": exp1["id"]},
        #     {"object_type": "experiment", "object_id": exp2["id"]},
        # ]

        ts_response = self._make_typescript_request(objects)
        bs_response = self._make_brainstore_request(objects)

        self.verify_response_equivalence(ts_response, bs_response, "cross_org_boundary")

        # Verify that only project1 has a policy
        ts_data = ts_response.json()

        # In the new format, only objects WITH policies are included
        project1_key = f"project_logs:{project1['id']}"
        project2_key = f"project_logs:{project2['id']}"

        self.assertIn(project1_key, ts_data, "Project1 should have retention policy from org1")
        self.assertNotIn(project2_key, ts_data, "Project2 should NOT have retention policy from org1 and be omitted")

        # Verify project1's policy details
        project1_policy = ts_data[project1_key]
        self.assertIsNotNone(project1_policy, "project1 should have a policy")
        self.assertEqual(project1_policy["retention_days"], 30)

    def test_deleted_automations_not_returned(self):
        """Test that deleted retention automations are not returned in policy lookups"""
        project = self.run_request("post", f"{LOCAL_API_URL}/v1/project", json={"name": "test-project"}).json()

        # Create a retention automation
        first_automation = self._create_automation(
            project["id"],
            dict(
                name="first-policy",
                description="First retention policy",
                config=dict(
                    event_type="retention",
                    object_type="project_logs",
                    retention_days=30,
                ),
            ),
        ).json()

        # Verify it works
        objects = [{"object_type": "project_logs", "object_id": project["id"]}]
        ts_response = self._make_typescript_request(objects)
        exp_key = f"project_logs:{project['id']}"
        self.assertIn(exp_key, ts_response.json())
        self.assertEqual(ts_response.json()[exp_key]["retention_days"], 30)

        # Delete the automation
        self.run_request(
            "delete",
            f"{LOCAL_API_URL}/v1/project_automation/{first_automation['project_automation']['id']}",
        )

        # Create another retention automation that should now be used
        fallback_automation = self._create_automation(
            project["id"],
            dict(
                name="fallback-policy",
                description="Fallback retention policy",
                config=dict(
                    event_type="retention",
                    object_type="project_logs",
                    retention_days=60,
                ),
            ),
        ).json()

        # Verify the new automation is used
        ts_response = self._make_typescript_request(objects)
        bs_response = self._make_brainstore_request(objects)

        self.verify_response_equivalence(ts_response, bs_response, "deleted_automations")

        # Verify that the deleted automation is not used, fallback is used instead
        ts_data = ts_response.json()
        self.assertEqual(len(ts_data), 1)

        exp_key = f"project_logs:{project['id']}"
        self.assertIn(exp_key, ts_data)
        policy = ts_data[exp_key]
        self.assertIsNotNone(policy)
        self.assertEqual(policy["retention_days"], 60, "Should use the fallback automation's retention days")
        self.assertEqual(
            policy["automation_id"],
            fallback_automation["project_automation"]["id"],
            "Should use the fallback automation's ID",
        )

    def test_empty_request_array(self):
        """Test behavior with empty request array"""
        objects = []

        ts_response = self._make_typescript_request(objects)
        bs_response = self._make_brainstore_request(objects)

        self.verify_response_equivalence(ts_response, bs_response, "empty_request")

        # Should return empty dict (not array)
        self.assertEqual(ts_response.json(), {})
        self.assertEqual(bs_response.json(), {})

    def test_duplicate_objects_in_request(self):
        """Test behavior when the same object is requested multiple times"""
        project = self.run_request("post", f"{LOCAL_API_URL}/v1/project", json={"name": "test-project"}).json()

        self._create_automation(
            project["id"],
            dict(
                name="test-retention",
                description="Test retention policy",
                config=dict(
                    event_type="retention",
                    object_type="project_logs",
                    retention_days=30,
                ),
            ),
        )

        # Request the same project multiple times
        objects = [
            {"object_type": "project_logs", "object_id": project["id"]},
            {"object_type": "project_logs", "object_id": project["id"]},
            {"object_type": "project_logs", "object_id": project["id"]},
        ]

        ts_response = self._make_typescript_request(objects)
        bs_response = self._make_brainstore_request(objects)

        self.verify_response_equivalence(ts_response, bs_response, "duplicate_objects")

        # In the new format, duplicates are automatically deduplicated in the mapping
        # So we expect only 1 entry for the project
        ts_data = ts_response.json()
        self.assertEqual(len(ts_data), 1, "Should deduplicate to 1 unique entry")

        # Verify the project has the expected policy
        exp_key = f"project_logs:{project['id']}"
        self.assertIn(exp_key, ts_data)
        policy = ts_data[exp_key]
        self.assertIsNotNone(policy)
        self.assertEqual(policy["retention_days"], 30)

    def test_deleted_objects(self):
        """Test behavior when requesting policies for deleted objects"""
        # Create and then delete a project
        project = self.run_request("post", f"{LOCAL_API_URL}/v1/project", json={"name": "test-project"}).json()

        # Create retention policy
        self._create_automation(
            project["id"],
            dict(
                name="test-retention",
                description="Test retention policy",
                config=dict(
                    event_type="retention",
                    object_type="project_logs",
                    retention_days=30,
                ),
            ),
        )

        # Delete the project
        self.run_request("delete", f"{LOCAL_API_URL}/v1/project/{project['id']}")

        # Request policy for deleted project
        objects = [{"object_type": "project_logs", "object_id": project["id"]}]

        ts_response = self._make_typescript_request(objects)
        bs_response = self._make_brainstore_request(objects)

        self.verify_response_equivalence(ts_response, bs_response, "deleted_objects")

        # The retention policy should be deleted because the project was deleted
        bs_data = bs_response.json()
        exp_key = f"project_logs:{project['id']}"
        self.assertNotIn(exp_key, bs_data, "Policy should not exist for deleted project")

        # TODO(object_type): Test policy on non-project object still exists after deleting object
