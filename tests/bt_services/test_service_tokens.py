import uuid

from braintrust_local.util import ensure_not_none

from tests.braintrust_app_test_base import LOCAL_API_URL, LOCAL_APP_URL, BraintrustAppTestBase


class ServiceTokensTest(BraintrustAppTestBase):
    def test_basic(self):
        service_account_id, _, service_token = self.createServiceAccountInOrg(
            self.org_id, token_name="service token 1", remove_from_org_owners=True
        )
        service_account_email = self.getUserEmail(service_account_id)
        # should be expected format for service account non-emails
        assert service_account_email.startswith("bt::sp::custom::")
        assert "@" not in service_account_email

        service_tokens = self.run_server_action(
            self.org_api_key, "fetchServiceTokensAsOrgOwner", dict(org_id=self.org_id)
        ).json()
        self.assertEqual(
            {(x["user_email"], x["name"]) for x in service_tokens},
            {
                (service_account_email, "service token 1"),
                (self.getUserEmail(self.service_account_id), "service token"),
            },
        )

        with self.assertRaises(Exception):
            self.run_server_action(
                service_token,
                "deleteServiceTokenAsOrgOwner",
                dict(org_id=self.org_id, service_token_id=service_tokens[0]["id"]),
            )

        org_api_key_id = self.run_server_action(self.org_api_key, "fetchApiKeys", dict()).json()[0]["id"]
        org_api_keys = self.run_server_action(
            self.org_api_key, "fetchApiKeysAsOrgOwner", dict(org_id=self.org_id, user_type="user")
        ).json()
        assert set(
            [(x["id"], x["user_given_name"], x["user_family_name"], x["user_email"]) for x in org_api_keys]
        ) == {(org_api_key_id, None, None, self.getUserEmail(self.user_id))}

        for unprivileged_api_key in [service_token]:
            resp = self.run_server_action(
                unprivileged_api_key, "fetchApiKeysAsOrgOwner", dict(org_id=self.org_id)
            ).json()
            assert resp == []

        _, _, user1_api_key = self.createUserInOrg(self.org_id, remove_from_org_owners=True)
        user1_api_key_id = self.run_server_action(user1_api_key, "fetchApiKeys", dict()).json()[0]["id"]
        self.run_server_action(
            self.service_token, "deleteApiKeyAsOrgOwner", dict(org_id=self.org_id, api_key_id=user1_api_key_id)
        )

        _, _, api_key = self.createUserInOrg(self.org_id, remove_from_org_owners=True)
        api_key_id = self.run_server_action(api_key, "fetchApiKeys", dict()).json()[0]["id"]
        with self.assertRaises(Exception):
            self.run_server_action(
                api_key,
                "deleteServiceTokenAsOrgOwner",
                dict(org_id=self.org_id, service_token_id=service_tokens[0]["id"]),
            )

        org2_id, _ = self.createOrg()
        self.addUserToOrg(service_account_id, org2_id)

        org2_service_tokens = self.run_server_action(
            service_token, "fetchServiceTokensAsOrgOwner", dict(org_id=org2_id)
        ).json()
        assert len(org2_service_tokens) == 0

    def test_service_token_lifecycle(self):
        """Test complete lifecycle of service tokens and accounts"""
        # Create service account with initial token
        service_account_id, _, service_token = self.createServiceAccountInOrg(
            self.org_id, token_name="initial token", remove_from_org_owners=True
        )

        # Create additional tokens for same account
        token2 = self.run_request(
            "post",
            f"{LOCAL_APP_URL}/api/service_token/register",
            headers=dict(Authorization=f"Bearer {self.org_api_key}"),
            json=dict(org_id=self.org_id, account_id=service_account_id, name="second token"),
        ).json()["service_token"]["key"]

        token3 = self.run_request(
            "post",
            f"{LOCAL_APP_URL}/api/service_token/register",
            headers=dict(Authorization=f"Bearer {self.org_api_key}"),
            json=dict(org_id=self.org_id, account_id=service_account_id, name="third token"),
        ).json()["service_token"]["key"]

        # Verify all tokens work
        for token in [service_token, token2, token3]:
            resp = self.run_request(
                "post", f"{LOCAL_APP_URL}/api/self/me", headers=dict(Authorization=f"Bearer {token}"), json={}
            )
            assert resp.status_code == 200
            assert resp.json()["id"] == service_account_id

        # Delete one token
        tokens = self.run_server_action(
            self.org_api_key, "fetchServiceTokensAsOrgOwner", dict(org_id=self.org_id)
        ).json()
        token_to_delete = next(t for t in tokens if t["name"] == "second token")

        self.run_server_action(
            self.org_api_key,
            "deleteServiceTokenAsOrgOwner",
            dict(org_id=self.org_id, service_token_id=token_to_delete["id"]),
        )

        # Verify deleted token no longer works
        resp = self.run_request(
            "post",
            f"{LOCAL_APP_URL}/api/self/me",
            headers=dict(Authorization=f"Bearer {token2}"),
            json={},
            expect_error=True,
        )
        assert resp.status_code == 401

        # Other tokens should still work
        for token in [service_token, token3]:
            resp = self.run_request(
                "post", f"{LOCAL_APP_URL}/api/self/me", headers=dict(Authorization=f"Bearer {token}"), json={}
            )
            assert resp.status_code == 200

    def test_service_token_permissions(self):
        """Test permission boundaries for service token operations"""
        # Create multiple users with different permission levels
        owner_id, _, owner_api_key = self.createUserInOrg(self.org_id)
        viewer_id, _, viewer_api_key = self.createUserInOrg(self.org_id, remove_from_org_owners=True)

        # Create service account as org owner
        service_account_id, _, service_token = self.createServiceAccountInOrg(self.org_id, token_name="test token")

        # Non-owner cannot fetch service tokens
        with self.assertRaises(Exception):
            self.run_server_action(viewer_api_key, "fetchServiceTokensAsOrgOwner", dict(org_id=self.org_id))

        # Non-owner cannot create service tokens
        with self.assertRaises(Exception):
            resp = self.run_request(
                "post",
                f"{LOCAL_APP_URL}/api/service_token/register",
                headers=dict(Authorization=f"Bearer {viewer_api_key}"),
                json=dict(org_id=self.org_id, account_id=service_account_id, name="unauthorized"),
            )
            assert resp.status_code == 401

        # Non-owner cannot delete service tokens
        with self.assertRaises(Exception):
            self.run_server_action(
                viewer_api_key,
                "deleteServiceTokenAsOrgOwner",
                dict(org_id=self.org_id, service_token_id=service_token["id"]),
            )

        # Owner service account cannot manage its own tokens
        resp = self.run_server_action(service_token, "fetchServiceTokensAsOrgOwner", dict(org_id=self.org_id)).json()
        assert len(resp) == 2

        # Owner service account can delete tokens
        tokens = self.run_server_action(owner_api_key, "fetchServiceTokensAsOrgOwner", dict(org_id=self.org_id)).json()

        self.run_server_action(
            service_token, "deleteServiceTokenAsOrgOwner", dict(org_id=self.org_id, service_token_id=tokens[0]["id"])
        )

        tokens = self.run_server_action(owner_api_key, "fetchServiceTokensAsOrgOwner", dict(org_id=self.org_id)).json()
        assert len(tokens) == 1

    def test_service_token_cross_org(self):
        """Test service tokens across multiple organizations"""
        # Create second org
        org2_id, _ = self.createOrg()
        self.addUserToOrg(self.user_id, org2_id)
        org2_api_key = self.createUserOrgApiKey(self.user_id, org2_id)

        # Create service account in org1
        service_account_id, _, service_token = self.createServiceAccountInOrg(
            self.org_id, token_name="org1 token", remove_from_org_owners=True
        )

        # Add service account to org2
        self.addUserToOrg(service_account_id, org2_id)

        # Service token should work for both orgs
        for org_id in [self.org_id, org2_id]:
            resp = self.run_request(
                "post",
                f"{LOCAL_APP_URL}/api/organization/get",
                headers=dict(Authorization=f"Bearer {service_token}"),
                json=dict(id=[org_id]),
            )
            assert resp.status_code == 200

        service_account_id2, _ = self.createServiceAccount()
        self.addUserToOrg(service_account_id2, org2_id)

        # Create org2-scoped token for same service account
        org2_token = self.run_request(
            "post",
            f"{LOCAL_APP_URL}/api/service_token/register",
            headers=dict(Authorization=f"Bearer {org2_api_key}"),
            json=dict(org_id=org2_id, account_id=service_account_id2, name="org2 token"),
        ).json()["service_token"]["key"]

        # Org2 token should only work for org2
        resp = self.run_request(
            "post",
            f"{LOCAL_APP_URL}/api/organization/get",
            headers=dict(Authorization=f"Bearer {org2_token}"),
            json=dict(id=[org2_id]),
        )
        assert resp.status_code == 200
        assert len(resp.json()) == 1

        # Should fail for org1
        resp = self.run_request(
            "post",
            f"{LOCAL_APP_URL}/api/organization/get",
            headers=dict(Authorization=f"Bearer {org2_token}"),
            json=dict(id=[self.org_id]),
        )
        assert resp.status_code == 200
        assert len(resp.json()) == 0

    def test_service_token_edge_cases(self):
        """Test edge cases and error handling"""
        # Test empty name
        with self.assertRaises(Exception):
            self.run_request(
                "post",
                f"{LOCAL_APP_URL}/api/service_token/register",
                headers=dict(Authorization=f"Bearer {self.org_api_key}"),
                json=dict(org_id=self.org_id, account_id=self.service_account_id, name=""),
            )

        # Test very long name
        long_name = "a" * 1000
        service_account_id, _, _ = self.createServiceAccountInOrg(
            self.org_id, token_name=long_name[:255], remove_from_org_owners=True  # Assuming 255 char limit
        )

        # Test duplicate token names for same account
        resp = self.run_request(
            "post",
            f"{LOCAL_APP_URL}/api/service_token/register",
            headers=dict(Authorization=f"Bearer {self.org_api_key}"),
            json=dict(org_id=self.org_id, account_id=service_account_id, name="duplicate"),
        ).json()
        service_token_id = resp["service_token"]["id"]
        service_token = resp["service_token"]["key"]

        # Should allow duplicate names (tokens are distinguished by ID)
        resp = self.run_request(
            "post",
            f"{LOCAL_APP_URL}/api/service_token/register",
            headers=dict(Authorization=f"Bearer {self.org_api_key}"),
            json=dict(org_id=self.org_id, account_id=service_account_id, name="duplicate"),
        ).json()
        assert resp["service_token"]["key"] != service_token
        service_token_id2 = resp["service_token"]["id"]
        service_token2 = resp["service_token"]["key"]

        # Test deleting non-existent token
        with self.assertRaises(Exception):
            self.run_server_action(
                self.org_api_key,
                "deleteServiceTokenAsOrgOwner",
                dict(org_id=self.org_id, service_token_id=str(uuid.uuid4())),
            )

        # Test removing service account with active tokens
        self.run_server_action(
            self.org_api_key, "removeMembers", dict(orgId=self.org_id, users=dict(ids=[service_account_id]))
        )

        # Tokens should no longer work
        org_api_key_id = self.run_server_action(self.org_api_key, "fetchApiKeys", dict()).json()[0]["id"]
        for token in [service_token, service_token2]:
            resp = self.run_server_action(
                token,
                "deleteApiKey",
                dict(org_id=self.org_id, api_key_id=org_api_key_id),
                expect_error=True,
            )
            assert resp.status_code == 400

    def test_get_service_tokens_api(self):
        """Test fetching service tokens through the GET API endpoint"""
        # Create multiple service accounts with tokens
        service_account_id1, _, service_token1 = self.createServiceAccountInOrg(
            self.org_id, token_name="test token 1", remove_from_org_owners=True
        )
        service_account_id2, _, service_token2 = self.createServiceAccountInOrg(
            self.org_id, token_name="test token 2", remove_from_org_owners=True
        )

        # Create additional token for first account
        token3 = self.run_request(
            "post",
            f"{LOCAL_APP_URL}/api/service_token/register",
            headers=dict(Authorization=f"Bearer {self.org_api_key}"),
            json=dict(org_id=self.org_id, account_id=service_account_id1, name="test token 3"),
        ).json()["service_token"]["key"]

        # Test fetching all service tokens as org owner
        resp = self.run_request(
            "post",
            f"{LOCAL_APP_URL}/api/service_token/get",
            headers=dict(Authorization=f"Bearer {self.org_api_key}"),
            json=dict(org_id=self.org_id),
        )
        assert resp.status_code == 200
        tokens = resp.json()["service_tokens"]

        # Should have tokens from setup + new ones
        token_names = {t["name"] for t in tokens}
        assert "test token 1" in token_names
        assert "test token 2" in token_names
        assert "test token 3" in token_names
        assert "service token" in token_names  # from setup

        # Test filtering by name
        resp = self.run_request(
            "post",
            f"{LOCAL_APP_URL}/api/service_token/get",
            headers=dict(Authorization=f"Bearer {self.org_api_key}"),
            json=dict(org_id=self.org_id, name=["test token 1"]),
        )
        assert resp.status_code == 200
        tokens = resp.json()["service_tokens"]
        assert len(tokens) == 1
        assert tokens[0]["name"] == "test token 1"

        # Test filtering by id
        # First get all tokens to find the ID
        all_tokens_resp = self.run_request(
            "post",
            f"{LOCAL_APP_URL}/api/service_token/get",
            headers=dict(Authorization=f"Bearer {self.org_api_key}"),
            json=dict(org_id=self.org_id),
        )
        token_id = [t["id"] for t in all_tokens_resp.json()["service_tokens"] if t["name"] == "test token 1"][0]
        resp = self.run_request(
            "post",
            f"{LOCAL_APP_URL}/api/service_token/get",
            headers=dict(Authorization=f"Bearer {self.org_api_key}"),
            json=dict(org_id=self.org_id, id=[token_id]),
        )
        assert resp.status_code == 200
        tokens = resp.json()["service_tokens"]
        assert len(tokens) == 1
        assert tokens[0]["id"] == token_id

        # Test non-org-owner gets 400
        _, _, regular_user_token = self.createUserInOrg(self.org_id, remove_from_org_owners=True)
        resp = self.run_request(
            "post",
            f"{LOCAL_APP_URL}/api/service_token/get",
            headers=dict(Authorization=f"Bearer {regular_user_token}"),
            json=dict(org_id=self.org_id),
        )
        assert resp.status_code == 200
        assert resp.json()["service_tokens"] == []

        # Test service account itself cannot fetch tokens
        resp = self.run_request(
            "post",
            f"{LOCAL_APP_URL}/api/service_token/get",
            headers=dict(Authorization=f"Bearer {service_token1}"),
            json=dict(org_id=self.org_id),
        )
        assert resp.status_code == 200
        assert resp.json()["service_tokens"] == []

        # Test pagination
        resp = self.run_request(
            "post",
            f"{LOCAL_APP_URL}/api/service_token/get",
            headers=dict(Authorization=f"Bearer {self.org_api_key}"),
            json=dict(org_id=self.org_id, limit=2),
        )
        assert resp.status_code == 200
        tokens = resp.json()["service_tokens"]
        assert len(tokens) == 4

        # Should have tokens from setup + new ones
        token_names = {t["name"] for t in tokens}
        assert "test token 1" in token_names
        assert "test token 2" in token_names
        assert "test token 3" in token_names
        assert "service token" in token_names  # from setup

    def test_delete_service_token_api(self):
        """Test deleting service tokens through the DELETE API endpoint"""
        # Create service account with tokens
        service_account_id, _, service_token1 = self.createServiceAccountInOrg(
            self.org_id, token_name="delete test 1", remove_from_org_owners=True
        )

        # Create additional token
        resp = self.run_request(
            "post",
            f"{LOCAL_APP_URL}/api/service_token/register",
            headers=dict(Authorization=f"Bearer {self.org_api_key}"),
            json=dict(org_id=self.org_id, account_id=service_account_id, name="delete test 2"),
        )
        service_token2 = resp.json()["service_token"]["key"]
        service_token2_id = resp.json()["service_token"]["id"]

        # Verify token works before deletion
        resp = self.run_request(
            "post",
            f"{LOCAL_APP_URL}/api/self/me",
            headers=dict(Authorization=f"Bearer {service_token2}"),
            json={},
        )
        assert resp.status_code == 200

        # Delete the token as org owner
        resp = self.run_request(
            "post",
            f"{LOCAL_APP_URL}/api/service_token/delete_id",
            headers=dict(Authorization=f"Bearer {self.org_api_key}"),
            json=dict(org_id=self.org_id, id=service_token2_id),
        )
        assert resp.status_code == 200
        deleted_token = resp.json()["service_token"]
        assert deleted_token["id"] == service_token2_id
        assert deleted_token["name"] == "delete test 2"

        # Verify token no longer works
        resp = self.run_request(
            "post",
            f"{LOCAL_APP_URL}/api/self/me",
            headers=dict(Authorization=f"Bearer {service_token2}"),
            json={},
            expect_error=True,
        )
        assert resp.status_code == 401
        assert "Invalid API Key" in resp.text

        # Verify first token still works
        resp = self.run_request(
            "post",
            f"{LOCAL_APP_URL}/api/self/me",
            headers=dict(Authorization=f"Bearer {service_token1}"),
            json={},
        )
        assert resp.status_code == 200

        # Test non-org-owner gets 400
        _, _, regular_user_token = self.createUserInOrg(self.org_id, remove_from_org_owners=True)
        resp = self.run_request(
            "post",
            f"{LOCAL_APP_URL}/api/service_token/delete_id",
            headers=dict(Authorization=f"Bearer {regular_user_token}"),
            json=dict(org_id=self.org_id, id=service_token2_id),
            expect_error=True,
        )
        assert resp.status_code == 400

        # Test service account cannot delete tokens
        resp = self.run_request(
            "post",
            f"{LOCAL_APP_URL}/api/service_token/delete_id",
            headers=dict(Authorization=f"Bearer {service_token1}"),
            json=dict(org_id=self.org_id, id=service_token2_id),
            expect_error=True,
        )
        assert resp.status_code == 400

        # Test deleting non-existent token
        resp = self.run_request(
            "post",
            f"{LOCAL_APP_URL}/api/service_token/delete_id",
            headers=dict(Authorization=f"Bearer {self.org_api_key}"),
            json=dict(org_id=self.org_id, id=str(uuid.uuid4())),
            expect_error=True,
        )
        assert resp.status_code == 400

        # Test deleting token from wrong org
        org2_id, _ = self.createOrg()
        self.addUserToOrg(self.user_id, org2_id)
        org2_api_key = self.createUserOrgApiKey(self.user_id, org2_id)

        # Get a valid token ID from org1
        resp = self.run_request(
            "post",
            f"{LOCAL_APP_URL}/api/service_token/get",
            headers=dict(Authorization=f"Bearer {self.org_api_key}"),
            json=dict(org_id=self.org_id, limit=1),
        )
        org1_token_id = resp.json()["service_tokens"][0]["id"]

        # Try to delete org1's token using org2's credentials
        resp = self.run_request(
            "post",
            f"{LOCAL_APP_URL}/api/service_token/delete_id",
            headers=dict(Authorization=f"Bearer {org2_api_key}"),
            json=dict(org_id=org2_id, id=org1_token_id),
            expect_error=True,
        )
        assert resp.status_code == 400
